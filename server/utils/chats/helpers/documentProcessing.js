const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const { TokenManager } = require("../../helpers/tiktoken");
const { SystemSettings } = require("../../../models/systemSettings");
const { safeJsonParse } = require("../../http");
const { ContextWindowManager } = require("./contextWindowManager");

function updateLastTokens(result, LLM) {
  if (!result || !LLM) return;
  const tokens =
    result.metrics?.total_tokens ??
    (result.metrics?.prompt_tokens || 0) +
      (result.metrics?.completion_tokens || 0);
  LLM.metrics = { ...(LLM.metrics || {}), lastCompletionTokens: tokens };
}

/**
 * Generate a detailed summary/description of a document relevant to a legal task.
 *
 * @param {string} docName - Name of the document
 * @param {string} content - Content of the document
 * @param {string} legalTask - The legal task description
 * @param {Object} LLMConnector - LLM provider instance
 * @param {Object} options - Additional options
 * @param {string} [options.customSystemPrompt] - Custom system prompt (overrides default)
 * @param {string} [options.customUserPromptTemplate] - Custom user prompt template (overrides default)
 * @param {number} [options.temperature=0.7] - Temperature for generation
 * @param {string} [chatId] - Optional chat ID for the filename
 * @param {string} [flowType='main'] - Type of flow (default 'main')
 * @returns {Promise<string>} - The generated description
 */
async function generateDocumentDescription(
  docName,
  content,
  legalTask,
  LLMConnector,
  options = {},
  chatId = null,
  flowType = "main"
) {
  // Calculate token count for metrics if needed
  const tm = new TokenManager(LLMConnector.model);
  const actualTokenCount = tm.countFromString(content);

  // Default prompts (can be overridden via options)
  const DEFAULT_SYSTEM_PROMPT =
    "You are an expert at analyzing legal documents and providing concise descriptions. Make a detailed summary of the document content and how it is relevant to the legal task. Write the summary in the same language as the legal task.";

  const DEFAULT_USER_PROMPT_TEMPLATE = `Generate a detailed summary of the following document content in relation to the legal task "{{task}}". Focus on the main topics and key information:\n\n{{content}}`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Fill template placeholders
  const userPrompt = fillTemplate(userPromptTemplate, {
    task: legalTask,
    content: content,
  });

  const compressedMessages = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const descriptionResult = await LLMConnector.getChatCompletion(
    compressedMessages,
    {
      temperature: options.temperature || 0.7,
    }
  );
  updateLastTokens(descriptionResult, LLMConnector);

  const generatedDescription = descriptionResult.textResponse;
  const tokensUsed = LLMConnector.metrics?.lastCompletionTokens || 0;

  return generatedDescription;
}

/**
 * Determine if a document is relevant to a legal task.
 *
 * @param {string} docName - Name of the document
 * @param {string} content - Content of the document
 * @param {string} legalTask - The legal task description
 * @param {Object} LLMConnector - LLM provider instance
 * @param {Object} options - Additional options
 * @param {string} [options.customSystemPrompt] - Custom system prompt
 * @param {string} [options.customUserPromptTemplate] - Custom user prompt template
 * @param {number} [options.temperature=0.7] - Temperature for generation
 * @returns {Promise<boolean>} - Whether the document is relevant
 */
async function generateDocumentRelevance(
  docName,
  content,
  legalTask,
  LLMConnector,
  options = {}
) {
  // Default prompts (can be overridden via options)
  const DEFAULT_SYSTEM_PROMPT =
    "You are an expert at evaluating whether a document is relevant to a legal task. Answer strictly with true or false. Respond in the same language used in the legal task prompt.";

  const DEFAULT_USER_PROMPT_TEMPLATE = `For the legal task "{{task}}", is the following document content relevant? Answer strictly "true" or "false":\n\n{{content}}`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Fill template placeholders
  const userPrompt = fillTemplate(userPromptTemplate, {
    task: legalTask,
    content: content,
  });

  const compressedMessages = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const relevanceResult = await LLMConnector.getChatCompletion(
    compressedMessages,
    {
      temperature: options.temperature || 0.7,
    }
  );
  updateLastTokens(relevanceResult, LLMConnector);

  return relevanceResult.textResponse.trim().toLowerCase().startsWith("true");
}

/**
 * Select which document is the "main" document based on descriptions.
 *
 * @param {Array<Object>} descriptions - Array of document descriptions
 * @param {string} legalTask - The legal task description
 * @param {Object} LLMConnector - LLM provider instance
 * @param {Object} options - Additional options
 * @param {string} [options.customSystemPrompt] - Custom system prompt
 * @param {string} [options.customUserPromptTemplate] - Custom user prompt template
 * @param {number} [options.temperature=0.7] - Temperature for generation
 * @returns {Promise<string>} - The selected main document name
 */
async function selectMainDocument(
  descriptions,
  legalTask,
  LLMConnector,
  options = {}
) {
  // Format the summaries for the prompt
  const listSummaries = descriptions
    .map((d) => `${d["Doc Name"]}: ${d.Description}`)
    .join("\n\n");

  // Default prompts (can be overridden via options)
  const DEFAULT_SYSTEM_PROMPT =
    "You are an expert legal assistant who selects the primary document among a set of summaries. Provide only the exact document name.";

  const DEFAULT_USER_PROMPT_TEMPLATE = `{{summaries}}\n\nGiven the summaries of documents for the legal task "{{task}}", which document is the main document? Return only the exact Doc Name.`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Fill template placeholders
  const userPrompt = fillTemplate(userPromptTemplate, {
    summaries: listSummaries,
    task: legalTask,
  });

  const compressed = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const result = await LLMConnector.getChatCompletion(compressed, {
    temperature: options.temperature || 0.7,
  });
  updateLastTokens(result, LLMConnector);

  return result.textResponse.trim();
}

/**
 * Save document descriptions to a JSON file.
 *
 * @param {Array<Object>} descriptions - Array of document descriptions
 * @param {string} [chatId] - Optional chat ID for the filename
 * @param {string} [subdirectory=''] - Optional subdirectory within document-builder
 * @returns {string} - The path to the saved file
 */
function saveDocumentDescriptions(descriptions, chatId, subdirectory = "") {
  // If no chatId is provided, use a timestamp
  const fileIdentifier = chatId || Date.now();
  const fileName = `document-descriptions-${fileIdentifier}.json`;

  // Create the base path and subdirectory if it doesn't exist
  let basePath = path.join(__dirname, "../../../storage/document-builder");

  if (subdirectory) {
    basePath = path.join(basePath, subdirectory);
  }

  if (!fs.existsSync(basePath)) {
    fs.mkdirSync(basePath, { recursive: true });
  }

  const filePath = path.join(basePath, fileName);

  try {
    const formattedContent = JSON.stringify(descriptions, null, 2);
    fs.writeFileSync(filePath, formattedContent, "utf8");
    console.log(`Document descriptions saved: ${filePath}`);
    return filePath;
  } catch (error) {
    console.error(`Error writing document descriptions file: ${error.message}`);
    throw error;
  }
}

/**
 * Generate section list from document summaries for the "no main document" flow.
 *
 * @param {Array<Object>} docSummaries - Array of document summaries
 * @param {string} legalTask - The legal task description
 * @param {Object} LLMConnector - LLM provider instance
 * @param {string} [customInstructions=''] - Custom instructions to add to the prompt
 * @param {Object} [options={}] - Additional options
 * @returns {Promise<Array<Object>>} - The generated section list
 */
async function generateSectionListFromSummaries(
  docSummaries,
  legalTask,
  LLMConnector,
  customInstructions = "",
  options = {}
) {
  // Default prompts specific to creating sections from multiple documents
  const DEFAULT_SYSTEM_PROMPT = `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided document summaries and legal task.
Return ONLY the JSON array (no markdown fences) where each element has:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_documents": array<string>
  - "legal_issues_to_address": array<string>

Example:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_documents": ["document1.pdf", "document2.docx"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]
Use the same language as the legal task for all free-text fields.`;

  const DEFAULT_USER_PROMPT_TEMPLATE = `Legal Task: {{task}}

Document Summaries:
{{summaries}}

Based on these document summaries, identify appropriate sections for a comprehensive legal document that addresses the task. Return only a JSON array with the structure specified.`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPromptTemplate =
    options.customUserPromptTemplate || DEFAULT_USER_PROMPT_TEMPLATE;

  // Format document summaries
  const formattedSummaries = docSummaries
    .map((doc) => `${doc["Doc Name"]}: ${doc.Description}`)
    .join("\n\n");

  // Fill template
  const baseUserPrompt = fillTemplate(userPromptTemplate, {
    task: legalTask,
    summaries: formattedSummaries,
  });

  // Add custom instructions if provided
  const userPrompt = customInstructions
    ? `${customInstructions}\n\n${baseUserPrompt}`
    : baseUserPrompt;

  const compressed = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const result = await LLMConnector.getChatCompletion(compressed, {
    temperature: options.temperature || 0.7,
  });
  updateLastTokens(result, LLMConnector);

  try {
    const text = result.textResponse.trim();
    const body = (text.match(/```(?:json)?\r?\n([\s\S]*?)```/) || [
      null,
      text,
    ])[1];
    return JSON.parse(body);
  } catch (err) {
    console.error(
      "generateSectionListFromSummaries: Failed to parse sections JSON",
      err
    );
    return [];
  }
}

/**
 * Map a document to relevant section indices.
 *
 * @param {string} docName - Name of the document
 * @param {string} content - Content of the document
 * @param {Array<Object>} sectionList - List of sections
 * @param {Object} LLMConnector - LLM provider instance
 * @param {Object} [options={}] - Additional options
 * @returns {Promise<Array<number>>} - Array of relevant section indices
 */
async function generateDocumentSectionIndices(
  docName,
  content,
  sectionList,
  LLMConnector,
  options = {}
) {
  if (!Array.isArray(sectionList) || sectionList.length === 0) return [];

  // Build a concise string of section identifiers for the LLM prompt
  const sectionLines = sectionList
    .map((s) => {
      const idx = s.index_number ?? s.index ?? s["Topic Number"] ?? "";
      const title = s.title ?? s.Title ?? s.Description ?? "";
      return `${idx}. ${title}`.trim();
    })
    .join("\n");

  const DEFAULT_SYSTEM_PROMPT = `You are a legal analyst. Decide which sections are supported by the given document content.
Return ONLY a JSON array of section index numbers (integers). No markdown fences.
Example output: [1,3,5]
Ensure any explanatory text (if unavoidable) is in the same language as the legal task.`;

  const systemPrompt = options.customSystemPrompt || DEFAULT_SYSTEM_PROMPT;
  const userPrompt = `Sections:\n${sectionLines}\n\nDocument (${docName}) content:\n${content}`;

  const compressed = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt,
  });

  const result = await LLMConnector.getChatCompletion(compressed, {
    temperature: options.temperature || 0,
  });
  updateLastTokens(result, LLMConnector);

  try {
    const trimmed = result.textResponse.trim();
    const body = (trimmed.match(/```(?:json)?\r?\n([\s\S]*?)```/i) || [
      null,
      trimmed,
    ])[1];
    const arr = JSON.parse(body);
    return Array.isArray(arr) ? arr : [];
  } catch (err) {
    console.error(
      `Failed to parse section index JSON for document ${docName}:`,
      err
    );
    return [];
  }
}

/**
 * Helper function to fill placeholders in prompt templates.
 *
 * @param {string} template - Template string with {{placeholders}}
 * @param {Object} vars - Object with key-value pairs for replacement
 * @returns {string} - Filled template
 */
function fillTemplate(template, vars) {
  return Object.entries(vars).reduce(
    (str, [key, val]) => str.split(`{{${key}}}`).join(val),
    template
  );
}

/**
 * Combines section outputs into a single markdown string.
 * @param {Array<{title: string, content: string}>} sectionOutputs - Array of section objects with title and content.
 * @returns {string} - The combined markdown string.
 */
function combineSectionOutputs(sectionOutputs = []) {
  if (!Array.isArray(sectionOutputs) || sectionOutputs.length === 0) {
    return ""; // Return empty string if input is invalid or empty
  }

  return sectionOutputs
    .map((sec) => {
      // Ensure title and content are strings, provide defaults if missing
      const title =
        typeof sec?.title === "string" ? sec.title : "Untitled Section";
      const content =
        typeof sec?.content === "string"
          ? sec.content
          : "(Content not available)";
      return `## ${title}\n\n${content}`;
    })
    .join("\n\n"); // Join sections seamlessly without separator
}

/**
 * Generate document description with iterative chunking for large documents
 *
 * @param {string} docName - Name of the document
 * @param {string} content - Content of the document
 * @param {string} legalTask - The legal task description
 * @param {Object} LLMConnector - LLM provider instance
 * @param {Object} options - Additional options including token limits
 * @returns {Promise<string>} - The generated description
 */
async function generateDocumentDescriptionIterative(
  docName,
  content,
  legalTask,
  LLMConnector,
  options = {}
) {
  const contextManager = new ContextWindowManager(LLMConnector, {
    ...options,
    logTokenUsage: options.logTokenUsage || false,
  });
  const tm = new TokenManager(LLMConnector.model);
  const tokenTracker = contextManager.getTokenTracker();

  // Start tracking this stage
  const stageTracker = tokenTracker
    ? tokenTracker.startStage("documentDescriptions")
    : null;

  // Calculate token budget for description generation
  const systemPrompt =
    options.customSystemPrompt ||
    "You are an expert at analyzing legal documents and providing concise descriptions. Make a detailed summary of the document content and how it is relevant to the legal task.";

  const userPromptTemplate =
    options.customUserPromptTemplate ||
    `Generate a detailed summary of the following document content in relation to the legal task "{{task}}". Focus on the main topics and key information:\n\n{{content}}`;

  const budget = contextManager.calculateTokenBudget({
    systemPrompt,
    userPromptTemplate: fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: "",
    }),
    reservedTokens: options.reservedTokens || 2000,
  });

  const actualTokenCount = tm.countFromString(content);

  // Track content tokens
  if (tokenTracker) {
    tokenTracker.trackContentTokens(content, "documents", docName);
    tokenTracker.trackContentTokens(
      systemPrompt,
      "systemPrompts",
      "document-description"
    );
    tokenTracker.trackBudget(budget, actualTokenCount);
  }

  // If content fits within budget, use standard processing
  if (actualTokenCount <= budget.availableForContent) {
    const result = await generateDocumentDescription(
      docName,
      content,
      legalTask,
      LLMConnector,
      options
    );

    // Track the LLM response
    if (tokenTracker && LLMConnector.metrics?.lastCompletionTokens) {
      stageTracker?.addTokens(
        LLMConnector.metrics.lastCompletionTokens,
        "standard-processing"
      );
    }

    stageTracker?.finish();
    return result;
  }

  console.log(
    `[ITERATIVE] Document ${docName} (${actualTokenCount} tokens) exceeds budget (${budget.availableForContent}). Using iterative processing.`
  );

  // Chunk the content
  const chunks = contextManager.chunkContent(content, {
    maxTokensPerChunk: budget.availableForContent,
    overlapTokens: options.overlapTokens || 200,
  });

  const allDescriptions = [];

  // Process each chunk
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const chunkUserPrompt = fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: `Document: ${docName} (Part ${i + 1} of ${chunks.length})\n\n${chunk.content}`,
    });

    // Track chunk processing
    if (tokenTracker) {
      tokenTracker.trackContentTokens(
        chunk.content,
        "documents",
        `${docName}-chunk-${i + 1}`
      );
    }

    const compressedMessages = await LLMConnector.compressMessages({
      systemPrompt,
      userPrompt: chunkUserPrompt,
    });

    const result = await LLMConnector.getChatCompletion(compressedMessages, {
      temperature: options.temperature || 0.7,
    });

    updateLastTokens(result, LLMConnector);

    // Track LLM response
    if (tokenTracker) {
      tokenTracker.trackLLMResponse(
        result,
        "documentDescriptions",
        `chunk-${i + 1}`
      );
    }

    if (stageTracker) {
      stageTracker.addTokens(
        LLMConnector.metrics?.lastCompletionTokens || 0,
        `chunk-${i + 1}`
      );
    }

    if (result.textResponse) {
      allDescriptions.push(result.textResponse);
    }
  }

  stageTracker?.finish();

  // Combine descriptions with clear separators
  return allDescriptions.join("\n\n---\n\n");
}

/**
 * Generate document relevance check with iterative chunking for large documents
 *
 * @param {string} docName - Name of the document
 * @param {string} content - Content of the document
 * @param {string} legalTask - The legal task description
 * @param {Object} LLMConnector - LLM provider instance
 * @param {Object} options - Additional options including token limits
 * @returns {Promise<boolean>} - Whether the document is relevant
 */
async function generateDocumentRelevanceIterative(
  docName,
  content,
  legalTask,
  LLMConnector,
  options = {}
) {
  const contextManager = new ContextWindowManager(LLMConnector, {
    ...options,
    logTokenUsage: options.logTokenUsage || false,
  });
  const tm = new TokenManager(LLMConnector.model);
  const tokenTracker = contextManager.getTokenTracker();

  // Start tracking this stage
  const stageTracker = tokenTracker
    ? tokenTracker.startStage("relevanceChecking")
    : null;

  // Calculate token budget for relevance checking
  const systemPrompt =
    options.customSystemPrompt ||
    "You are an expert at evaluating whether a document is relevant to a legal task. Answer strictly with true or false.";

  const userPromptTemplate =
    options.customUserPromptTemplate ||
    `For the legal task "{{task}}", is the following document content relevant? Answer strictly "true" or "false":\n\n{{content}}`;

  const budget = contextManager.calculateTokenBudget({
    systemPrompt,
    userPromptTemplate: fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: "",
    }),
    reservedTokens: options.reservedTokens || 500,
  });

  const actualTokenCount = tm.countFromString(content);

  // Track content tokens
  if (tokenTracker) {
    tokenTracker.trackContentTokens(content, "documents", docName);
    tokenTracker.trackContentTokens(
      systemPrompt,
      "systemPrompts",
      "document-relevance"
    );
    tokenTracker.trackBudget(budget, actualTokenCount);
  }

  // If content fits within budget, use standard processing
  if (actualTokenCount <= budget.availableForContent) {
    const result = await generateDocumentRelevance(
      docName,
      content,
      legalTask,
      LLMConnector,
      options
    );

    // Track the LLM response
    if (tokenTracker && LLMConnector.metrics?.lastCompletionTokens) {
      stageTracker?.addTokens(
        LLMConnector.metrics.lastCompletionTokens,
        "standard-processing"
      );
    }

    stageTracker?.finish();
    return result;
  }

  console.log(
    `[ITERATIVE] Relevance check for ${docName} (${actualTokenCount} tokens) exceeds budget (${budget.availableForContent}). Using iterative processing.`
  );

  // Chunk the content
  const chunks = contextManager.chunkContent(content, {
    maxTokensPerChunk: budget.availableForContent,
    overlapTokens: options.overlapTokens || 100,
  });

  // Process each chunk - if ANY chunk is relevant, document is relevant
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const chunkUserPrompt = fillTemplate(userPromptTemplate, {
      task: legalTask,
      content: `Document: ${docName} (Part ${i + 1} of ${chunks.length})\n\n${chunk.content}`,
    });

    // Track chunk processing
    if (tokenTracker) {
      tokenTracker.trackContentTokens(
        chunk.content,
        "documents",
        `${docName}-chunk-${i + 1}`
      );
    }

    const compressedMessages = await LLMConnector.compressMessages({
      systemPrompt,
      userPrompt: chunkUserPrompt,
    });

    const result = await LLMConnector.getChatCompletion(compressedMessages, {
      temperature: options.temperature || 0.3,
    });

    updateLastTokens(result, LLMConnector);

    // Track LLM response
    if (tokenTracker) {
      tokenTracker.trackLLMResponse(
        result,
        "relevanceChecking",
        `chunk-${i + 1}`
      );
    }

    if (stageTracker) {
      stageTracker.addTokens(
        LLMConnector.metrics?.lastCompletionTokens || 0,
        `chunk-${i + 1}`
      );
    }

    // If any chunk is relevant, the document is relevant
    if (result.textResponse.trim().toLowerCase().startsWith("true")) {
      stageTracker?.finish();
      return true;
    }
  }

  stageTracker?.finish();

  // If no chunk was relevant, document is not relevant
  return false;
}

/**
 * High-level iterative section drafting processor that handles a list of sections
 * This is the function called by the flows (mainDoc.js, noMainDoc.js)
 *
 * @param {Array} sectionList - List of sections to process
 * @param {Object} options - Processing options
 * @param {string} options.legalTask - The legal task description
 * @param {Array} options.processedDocuments - Processed documents
 * @param {string} options.workspacePath - Path to workspace documents
 * @param {string} options.mainDocNameInitial - Main document name
 * @param {number} options.temperature - LLM temperature
 * @param {Object} options.AllPrompts - Resolved prompts object
 * @param {string} options.chatId - Chat ID
 * @param {string} options.documentBuilderBasePath - Base path for document builder
 * @param {Object} contextWindowManager - Context window manager instance
 * @param {Object} tokenTracker - Token tracker instance
 * @param {Object} callbacks - Callback functions
 * @returns {Promise<Array>} - Array of drafted sections
 */
async function processIterativeSectionDraftingList(
  sectionList,
  options,
  contextWindowManager,
  tokenTracker,
  callbacks = {}
) {
  const {
    legalTask,
    processedDocuments = [],
    workspacePath,
    temperature = 0.7,
    AllPrompts,
  } = options;

  const { onSectionProgress, onTokenUsage } = callbacks;

  // Get the section drafting prompts from AllPrompts
  const sectionDraftingPrompts =
    AllPrompts.CURRENT_DEFAULT_SECTION_DRAFTING || {
      SYSTEM_PROMPT:
        "You are a legal document drafter. Create professional legal content for the given section.",
      USER_PROMPT:
        "Draft section {{sectionNumber}} titled '{{title}}' for the legal task '{{task}}'. Use the provided documents: {{docs}}",
    };

  const draftedSections = [];

  for (let i = 0; i < sectionList.length; i++) {
    const section = sectionList[i];
    const sectionNumber = section.index_number || i + 1;
    const sectionTitle = section.title || `Section ${sectionNumber}`;

    if (onSectionProgress) {
      onSectionProgress(i, sectionTitle, "Starting");
    }

    // --- ENHANCED: Section drafting with keepalive events ---
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 2000; // 2 seconds
    const KEEPALIVE_INTERVAL = 20000; // 20 seconds

    let keepaliveInterval = null;
    let sectionResult = null;
    let lastError = null;

    try {
      // Start keepalive interval to prevent frontend timeout
      keepaliveInterval = setInterval(() => {
        if (onSectionProgress) {
          onSectionProgress(i, sectionTitle, "Drafting (in progress)");
        }
        console.log(
          `[SECTION DRAFTING] Keepalive: Section ${sectionNumber} - ${sectionTitle} is still being drafted...`
        );
      }, KEEPALIVE_INTERVAL);

      // Retry loop for section drafting
      for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
          console.log(
            `[SECTION DRAFTING] Section ${sectionNumber} (${sectionTitle}) - Attempt ${attempt}/${MAX_RETRIES}`
          );

          // Prepare documents for this section
          const relevantDocs = processedDocuments.filter(
            (doc) =>
              section.relevant_documents?.includes(doc["Doc Name"]) ||
              section.relevant_documents?.includes(
                doc["Doc Name"]?.replace(".json", "")
              ) ||
              section.relevantDocumentNames?.includes(doc["Doc Name"]) ||
              section.relevantDocumentNames?.includes(
                doc["Doc Name"]?.replace(".json", "")
              )
          );

          // Prepare memos for this section (if any)
          const relevantMemos = section.generatedMemos || [];

          // Create section context
          const sectionContext = {
            sectionNumber,
            sectionTitle,
            legalTask,
            neighborContext: `Section ${sectionNumber} of ${sectionList.length}`,
          };

          // Prepare documents for the processor
          const documentsForProcessor = relevantDocs.map((doc) => ({
            name: doc["Doc Name"] || doc.DisplayName || "Unknown Document",
            content: doc.Content || doc.pageContent || "",
          }));

          // Prepare memos for the processor
          const memosForProcessor = relevantMemos.map((memo) => ({
            issue: memo.issue || memo.Issue || "Legal Issue",
            content: memo.memo || memo.content || "",
          }));

          const startTime = Date.now();

          // Use the lower-level processIterativeSectionDrafting function
          const result = await processIterativeSectionDraftingSingle({
            documents: documentsForProcessor,
            memos: memosForProcessor,
            iteration: 1,
            previousResult: "",
            context: sectionContext,
            LLMConnector: contextWindowManager.LLMConnector,
            prompts: {
              systemPrompt: sectionDraftingPrompts.SYSTEM_PROMPT,
              userPrompt: sectionDraftingPrompts.USER_PROMPT,
            },
            temperature,
            tokenTracker,
          });

          const endTime = Date.now();
          const draftingTime = Math.round((endTime - startTime) / 1000);

          console.log(
            `[SECTION DRAFTING] Section ${sectionNumber} (${sectionTitle}) completed in ${draftingTime}s on attempt ${attempt}`
          );

          // Success - store result and break retry loop
          sectionResult = {
            ...section,
            draftedContent: result.content,
            tokenUsage: {
              totalTokens: result.tokensUsed,
              iterations: 1,
              isIterative: false,
            },
          };

          lastError = null;
          break; // Exit retry loop on success
        } catch (attemptError) {
          lastError = attemptError;
          console.error(
            `[SECTION DRAFTING] Section ${sectionNumber} attempt ${attempt} failed:`,
            attemptError.message
          );

          if (attempt < MAX_RETRIES) {
            console.log(
              `[SECTION DRAFTING] Retrying section ${sectionNumber} in ${RETRY_DELAY}ms...`
            );
            await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
          }
        }
      }

      // Clear keepalive interval
      if (keepaliveInterval) {
        clearInterval(keepaliveInterval);
        keepaliveInterval = null;
      }

      // Check if we have a successful result
      if (sectionResult && !lastError) {
        draftedSections.push(sectionResult);

        if (onSectionProgress) {
          onSectionProgress(i, sectionTitle, "Complete");
        }

        if (onTokenUsage) {
          onTokenUsage(i, sectionResult.tokenUsage);
        }
      } else {
        // All retries failed
        throw (
          lastError ||
          new Error(
            `Failed to draft section ${sectionNumber} after ${MAX_RETRIES} attempts`
          )
        );
      }
    } catch (error) {
      console.error(`Error drafting section ${sectionNumber}:`, error);

      // Clear keepalive interval on error
      if (keepaliveInterval) {
        clearInterval(keepaliveInterval);
        keepaliveInterval = null;
      }

      // Add a section with error content
      const errorSection = {
        ...section,
        draftedContent: `Error drafting section: ${error.message}`,
        tokenUsage: { totalTokens: 0, iterations: 0, isIterative: false },
      };

      draftedSections.push(errorSection);

      if (onSectionProgress) {
        onSectionProgress(i, sectionTitle, "Error");
      }
    }
  }

  return draftedSections;
}

/**
 * Iterative section drafting processor for use with ContextWindowManager
 *
 * @param {Object} params - Processing parameters
 * @param {Array} params.documents - Documents for the section
 * @param {Array} params.memos - Memos for the section
 * @param {number} params.iteration - Current iteration number
 * @param {string} params.previousResult - Result from previous iteration
 * @param {Object} params.context - Section context (title, number, etc.)
 * @param {Object} LLMConnector - LLM provider instance
 * @param {Object} prompts - System and user prompt templates
 * @param {number} temperature - Generation temperature
 * @returns {Promise<Object>} - Processing result
 */
async function processIterativeSectionDraftingSingle({
  documents = [],
  memos = [],
  iteration,
  previousResult = "",
  context = {},
  LLMConnector,
  prompts,
  temperature = 0.7,
  tokenTracker = null, // Passed from ContextWindowManager
}) {
  const { sectionNumber, sectionTitle, legalTask, neighborContext } = context;

  // Track stage if tokenTracker is available
  const operationName =
    iteration === 1 ? "initial-drafting" : "refinement-drafting";

  // Prepare content for this iteration
  let sectionContext = "Relevant Documents:\n";

  if (documents.length > 0) {
    for (const doc of documents) {
      sectionContext += `Document: ${doc.name}\n${doc.content}\n\n`;

      // Track document content tokens
      if (tokenTracker) {
        tokenTracker.trackContentTokens(
          doc.content,
          "documents",
          `${doc.name}-iter-${iteration}`
        );
      }
    }
  } else {
    sectionContext += "(No document content available for this iteration)\n";
  }

  sectionContext += "\nRelevant Legal Memos:\n";
  if (memos.length > 0) {
    for (const memo of memos) {
      sectionContext += `Memo for Issue: ${memo.issue}\n${memo.content}\n\n`;

      // Track memo content tokens
      if (tokenTracker) {
        tokenTracker.trackContentTokens(
          memo.content,
          "memos",
          `${memo.issue}-iter-${iteration}`
        );
      }
    }
  } else {
    sectionContext += "(No specific legal memos for this iteration)\n";
  }

  let userPrompt;

  if (iteration === 1) {
    // Initial iteration - create from scratch
    userPrompt = fillTemplate(prompts.initialUserPrompt || prompts.userPrompt, {
      sectionNumber: sectionNumber.toString(),
      title: sectionTitle,
      task: legalTask,
      docs: sectionContext,
      neighborContext: neighborContext || "(No neighboring sections defined)",
    });
  } else {
    // Refinement iteration - enhance existing content
    userPrompt = fillTemplate(prompts.refineUserPrompt || prompts.userPrompt, {
      sectionNumber: sectionNumber.toString(),
      title: sectionTitle,
      task: legalTask,
      previousDraft: previousResult,
      newContent: sectionContext,
      neighborContext: neighborContext || "(No neighboring sections defined)",
    });

    // Track previous result tokens
    if (tokenTracker && previousResult) {
      tokenTracker.trackContentTokens(
        previousResult,
        "previousResults",
        `section-${sectionNumber}-iter-${iteration - 1}`
      );
    }
  }

  // Track prompt tokens
  if (tokenTracker) {
    tokenTracker.trackContentTokens(
      prompts.systemPrompt,
      "systemPrompts",
      `section-drafting-iter-${iteration}`
    );
    tokenTracker.trackContentTokens(
      userPrompt,
      "userPrompts",
      `section-${sectionNumber}-iter-${iteration}`
    );
  }

  const compressedMessages = await LLMConnector.compressMessages({
    systemPrompt: prompts.systemPrompt,
    userPrompt,
  });

  const result = await LLMConnector.getChatCompletion(compressedMessages, {
    temperature,
  });

  updateLastTokens(result, LLMConnector);

  // Track LLM response
  if (tokenTracker) {
    tokenTracker.trackLLMResponse(
      result,
      "sectionDrafting",
      `section-${sectionNumber}-${operationName}`
    );
  }

  return {
    content: result.textResponse.trim(),
    tokensUsed: LLMConnector.metrics?.lastCompletionTokens || 0,
    iteration,
    documentsProcessed: documents.length,
    memosProcessed: memos.length,
    llmResponse: result, // Include LLM response for tracking
  };
}

module.exports = {
  generateDocumentDescription,
  generateDocumentRelevance,
  selectMainDocument,
  saveDocumentDescriptions,
  generateSectionListFromSummaries,
  generateDocumentSectionIndices,
  fillTemplate,
  combineSectionOutputs,
  // New iterative functions
  generateDocumentDescriptionIterative,
  generateDocumentRelevanceIterative,
  processIterativeSectionDraftingList,
  processIterativeSectionDraftingSingle,
};
