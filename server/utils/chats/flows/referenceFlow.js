const fs = require("fs");
const path = require("path");
const { getUserDocumentPathName } = require("../../../endpoints/document");
const { getLLMProvider } = require("../../helpers");
const { writeResponseChunk } = require("../../helpers/chat/responses");
const { getResolvedPrompts } = require("../helpers/promptManager");

const {
  generateDocumentDescription,
  generateSectionListFromSummaries,
  fillTemplate,
} = require("../helpers/documentProcessing");

async function runReferenceFilesFlow(options) {
  const { abortSignal = null } = options;

  const AllPrompts = await getResolvedPrompts();

  const checkAbort = () => {
    if (abortSignal && abortSignal.aborted) {
      throw new Error("Client aborted - terminating reference files flow.");
    }
  };
  const {
    request,
    response,
    workspace,
    message: legalTask,
    chatMode = "chat",
    user = null,
    thread = null,
    attachments = [],
    chatId,
    isCanvasChat = false,
    preventChatCreation = false,
    settings_suffix = "",
    invoice_ref,
    vectorSearchMode = "default",
    hasUploadedFile = false,
    displayMessage = null,
    useDeepSearch = false,
    cdbOptions = [],
  } = options;

  const customInstructions = cdbOptions[1] || "";
  const metrics = {};

  const sendProgress = (data = {}) => {
    // Early exit if client has aborted - handle gracefully
    try {
      checkAbort();
    } catch (e) {
      // Client has aborted - log and return silently instead of crashing
      console.log(
        `[REFERENCE FILES FLOW] Client aborted during progress update: ${e.message}`
      );
      return;
    }

    const translated = { ...data };

    if (
      translated.progress === undefined &&
      typeof translated.status === "string"
    ) {
      switch (translated.status) {
        case "starting":
        case "in_progress":
          translated.progress = -1; // loading state
          break;
        case "complete":
          translated.progress = 100; // done
          break;
        case "error":
          translated.progress = -2; // failed
          break;
        default:
          // keep undefined for unrecognised status
          break;
      }
    }

    try {
      writeResponseChunk(response, {
        uuid: chatId,
        type: "cdbProgress",
        flowType: "referenceFiles",
        ...translated,
      });
    } catch (err) {
      console.error(
        "[REFERENCE FILES FLOW] Failed to send progress chunk",
        err
      );
    }
  };

  let LLMConnector;
  if (process.env.LLM_PROVIDER_CDB) {
    let modelPrefCDB;
    switch (process.env.LLM_PROVIDER_CDB.toLowerCase()) {
      case "openai":
        modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB;
        break;
      case "anthropic":
        modelPrefCDB = process.env.ANTHROPIC_MODEL_PREF_CDB;
        break;
      case "gemini":
        modelPrefCDB = process.env.GEMINI_LLM_MODEL_PREF_CDB;
        break;
      case "lmstudio":
        modelPrefCDB = process.env.LMSTUDIO_MODEL_PREF_CDB;
        break;
      // Add other cases for different CDB providers if supported
      default:
        modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB; // Fallback model preference for CDB
    }
    LLMConnector = getLLMProvider({
      provider: process.env.LLM_PROVIDER_CDB,
      model: modelPrefCDB,
    });
  } else {
    LLMConnector = getLLMProvider(); // Standard system LLM
  }
  const temperature = workspace?.openAiTemp ?? LLMConnector.defaultTemp;

  const documentBuilderBasePath = path.join(
    __dirname,
    "../../../storage/document-builder"
  );

  // Ensure the directory exists before we start writing files.
  if (!fs.existsSync(documentBuilderBasePath)) {
    fs.mkdirSync(documentBuilderBasePath, { recursive: true });
  }

  // Workspace path setup
  const folderName = getUserDocumentPathName(user, true, workspace.slug);
  const workspacePath = path.join(
    __dirname,
    "../../../storage/documents",
    folderName
  );

  if (!fs.existsSync(workspacePath)) {
    console.error(
      "[REFERENCE FILES FLOW] Workspace path not found:",
      workspacePath
    );
    sendProgress({
      step: 0,
      status: "error",
      message: "Workspace documents not found.",
    });
    writeResponseChunk(response, {
      uuid: chatId,
      type: "text",
      text: "Error: Workspace documents not found.",
      sources: [],
      close: true,
    });
    return;
  }

  // Read all JSON files and split into reference vs review sets
  const allDocFiles = fs
    .readdirSync(workspacePath)
    .filter((file) => file.endsWith(".json"));
  const refFiles = Array.isArray(cdbOptions[4]) ? cdbOptions[4] : [];
  const refSet = new Set(refFiles);
  const reviewFiles = allDocFiles.filter((file) => !refSet.has(file));

  sendProgress({
    step: 1,
    status: "starting",
    message: "Processing reference files...",
    total: refFiles.length,
  });

  // Generate descriptions for reference files
  const refDescriptions = [];
  for (let i = 0; i < refFiles.length; i++) {
    const file = refFiles[i];
    const filePath = path.join(workspacePath, file);

    if (!fs.existsSync(filePath)) continue;

    const displayName = file.replace(/\.json$/, "");

    // Emit loading state for this file
    sendProgress({
      step: 1,
      subStep: i + 1,
      total: refFiles.length,
      message: `Processing: ${displayName} - Generating description...`,
      progress: -1,
    });

    const content = fs.readFileSync(filePath, "utf8");
    const description = await generateDocumentDescription(
      file,
      content,
      legalTask,
      LLMConnector,
      {
        customSystemPrompt:
          AllPrompts.CURRENT_DEFAULT_REFERENCE_FILES_DESCRIPTION.SYSTEM_PROMPT,
        customUserPromptTemplate:
          AllPrompts.CURRENT_DEFAULT_REFERENCE_FILES_DESCRIPTION.USER_PROMPT,
        temperature,
      },
      chatId,
      "referenceFiles"
    );

    refDescriptions.push({
      "Doc Name": file,
      DisplayName: displayName,
      Description: description,
    });

    // Mark this reference file sub-step as completed
    sendProgress({
      step: 1,
      subStep: i + 1,
      total: refFiles.length,
      message: `Processed: ${displayName} - Generating description`,
      progress: 100,
    });
  }

  sendProgress({
    step: 1,
    status: "complete",
    message: "Reference files processed.",
  });
  sendProgress({
    step: 2,
    status: "starting",
    message: "Processing review files...",
    total: reviewFiles.length,
  });

  // Generate descriptions for review files
  const reviewDescriptions = [];
  for (let i = 0; i < reviewFiles.length; i++) {
    const file = reviewFiles[i];
    const filePath = path.join(workspacePath, file);

    if (!fs.existsSync(filePath)) continue;

    const displayName = file.replace(/\.json$/, "");

    // Emit loading state for this file
    sendProgress({
      step: 2,
      subStep: i + 1,
      total: reviewFiles.length,
      message: `Processing: ${displayName} - Generating description...`,
      progress: -1,
    });

    const content = fs.readFileSync(filePath, "utf8");
    const description = await generateDocumentDescription(
      file,
      content,
      legalTask,
      LLMConnector,
      {
        customSystemPrompt:
          AllPrompts.CURRENT_DEFAULT_REVIEW_FILES_DESCRIPTION.SYSTEM_PROMPT,
        customUserPromptTemplate:
          AllPrompts.CURRENT_DEFAULT_REVIEW_FILES_DESCRIPTION.USER_PROMPT,
        temperature,
      },
      chatId,
      "referenceFiles"
    );

    reviewDescriptions.push({
      "Doc Name": file,
      DisplayName: displayName,
      Description: description,
    });

    // Mark this review file sub-step as completed
    sendProgress({
      step: 2,
      subStep: i + 1,
      total: reviewFiles.length,
      message: `Processed: ${displayName} - Generating description`,
      progress: 100,
    });
  }

  sendProgress({
    step: 2,
    progress: 100,
    status: "complete",
    message: "Review files processed.",
  });

  // Combine reference and review descriptions into a single list
  const docDescriptions = [
    ...refDescriptions.filter(Boolean),
    ...reviewDescriptions.filter(Boolean),
  ];
  // Generate section list from summaries for reference flow
  sendProgress({
    step: 3,
    status: "starting",
    message: "Generating section list...",
  });
  const sectionList = await generateSectionListFromSummaries(
    docDescriptions,
    legalTask,
    LLMConnector,
    customInstructions,
    {
      customSystemPrompt:
        AllPrompts.CURRENT_DEFAULT_REFERENCE_REVIEW_SECTIONS.SYSTEM_PROMPT,
      customUserPromptTemplate:
        AllPrompts.CURRENT_DEFAULT_REFERENCE_REVIEW_SECTIONS.USER_PROMPT,
      temperature,
    }
  );
  sendProgress({
    step: 3,
    progress: 100,
    status: "complete",
    message: "Section list generated.",
  });

  let finalReport = "";
  let previouslyDraftedSections = "";
  sendProgress({
    step: 4,
    status: "starting",
    message: "Drafting sections...",
    ...(sectionList.length > 0 && { total: sectionList.length }),
  });

  // --- ENHANCED: Section drafting with keepalive events ---
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 2000; // 2 seconds
  const KEEPALIVE_INTERVAL = 20000; // 20 seconds

  for (
    let sectionIndex = 0;
    sectionIndex < sectionList.length;
    sectionIndex++
  ) {
    const section = sectionList[sectionIndex];
    if (!section || !section.title || !section.relevant_documents) continue;

    const sectionTitle = section.title;
    const sectionNumber = section.index_number || sectionIndex + 1;

    // Send starting progress for this section
    sendProgress({
      step: 4,
      message: `Drafting Section ${sectionNumber}: ${sectionTitle}...`,
      subStep: sectionIndex + 1,
      total: sectionList.length,
      status: "in_progress",
      progress: -1,
    });

    let keepaliveInterval = null;
    let draftedContent = null;
    let lastError = null;

    try {
      // Start keepalive interval to prevent frontend timeout
      keepaliveInterval = setInterval(() => {
        sendProgress({
          step: 4,
          message: `Drafting Section ${sectionNumber}: ${sectionTitle}...`,
          subStep: sectionIndex + 1,
          total: sectionList.length,
          status: "in_progress",
          progress: -1,
        });
        console.log(
          `[REFERENCE FILES FLOW] Keepalive: Section ${sectionNumber} - ${sectionTitle} is still being drafted...`
        );
      }, KEEPALIVE_INTERVAL);

      // Retry loop for section drafting
      for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
          console.log(
            `[REFERENCE FILES FLOW] Section ${sectionNumber} (${sectionTitle}) - Attempt ${attempt}/${MAX_RETRIES}`
          );

          let sectionContext = "";
          for (const docName of section.relevant_documents) {
            const docPath = path.join(workspacePath, docName);
            if (fs.existsSync(docPath)) {
              try {
                const rawContent = fs.readFileSync(docPath, "utf8");
                // Assuming the .json files store the actual content in a known structure, e.g., parsed.pageContent
                // For this example, I'll assume the raw content of the JSON is the text.
                // Adjust if your JSONs have a specific schema like { "pageContent": "..." }
                sectionContext += `Content from ${docName}:\n${rawContent}\n\n`;
              } catch (e) {
                console.warn(
                  `Could not read ${docName} for section ${section.title}: ${e.message}`
                );
              }
            }
          }

          // Use standardized reference section drafting prompts
          const systemPrompt =
            AllPrompts.CURRENT_DEFAULT_REFERENCE_SECTION_DRAFTING.SYSTEM_PROMPT;
          const userPrompt = fillTemplate(
            AllPrompts.CURRENT_DEFAULT_REFERENCE_SECTION_DRAFTING.USER_PROMPT,
            {
              sectionNumber: sectionNumber,
              title: section.title,
              task: legalTask,
              description: section.description || "",
              docs: sectionContext,
              previousSections:
                previouslyDraftedSections || "This is the first section.",
            }
          );

          const startTime = Date.now();
          const compressedMessages = await LLMConnector.compressMessages({
            systemPrompt: systemPrompt,
            userPrompt: userPrompt,
          });
          const sectionDraftResult = await LLMConnector.getChatCompletion(
            compressedMessages,
            { temperature }
          );
          const endTime = Date.now();
          const draftingTime = Math.round((endTime - startTime) / 1000);

          draftedContent =
            sectionDraftResult.textResponse ||
            "(Content could not be generated for this section)";

          // Validate result
          if (!draftedContent || draftedContent.length < 10) {
            throw new Error(
              `Section content too short: ${draftedContent?.length || 0} characters`
            );
          }

          console.log(
            `[REFERENCE FILES FLOW] Section ${sectionNumber} (${sectionTitle}) completed in ${draftingTime}s on attempt ${attempt}`
          );
          lastError = null;
          break; // Success, exit retry loop
        } catch (attemptError) {
          lastError = attemptError;
          console.error(
            `[REFERENCE FILES FLOW] Section ${sectionNumber} attempt ${attempt} failed:`,
            attemptError.message
          );

          if (attempt < MAX_RETRIES) {
            console.log(
              `[REFERENCE FILES FLOW] Retrying section ${sectionNumber} in ${RETRY_DELAY}ms...`
            );
            await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
          }
        }
      }

      // Clear keepalive interval
      if (keepaliveInterval) {
        clearInterval(keepaliveInterval);
        keepaliveInterval = null;
      }

      // Check if we have a successful result
      if (draftedContent && !lastError) {
        finalReport += `## ${section.title}\n\n${draftedContent}\n\n`;
        previouslyDraftedSections += `## ${section.title}\n\n${draftedContent}\n\n`; // Update for the next iteration

        // Send completion progress
        sendProgress({
          step: 4,
          message: `Drafted Section ${sectionNumber}: ${sectionTitle}`,
          subStep: sectionIndex + 1,
          total: sectionList.length,
          status: "complete",
          progress: 100,
        });
      } else {
        // All retries failed
        throw (
          lastError ||
          new Error(
            `Failed to draft section ${sectionNumber} after ${MAX_RETRIES} attempts`
          )
        );
      }
    } catch (error) {
      console.error(
        `[REFERENCE FILES FLOW] Error drafting section ${sectionNumber}:`,
        error
      );

      // Clear keepalive interval on error
      if (keepaliveInterval) {
        clearInterval(keepaliveInterval);
        keepaliveInterval = null;
      }

      // Add error content to report
      const errorContent = `Error drafting section: ${error.message}`;
      finalReport += `## ${section.title}\n\n${errorContent}\n\n`;
      previouslyDraftedSections += `## ${section.title}\n\n${errorContent}\n\n`;

      // Send error progress
      sendProgress({
        step: 4,
        message: `Error drafting Section ${sectionNumber}: ${sectionTitle}`,
        subStep: sectionIndex + 1,
        total: sectionList.length,
        status: "error",
        progress: -2,
      });
    }
  }
  sendProgress({
    step: 4,
    progress: 100,
    status: "complete",
    message: "Sections drafted.",
  });
  sendProgress({
    step: 5,
    status: "starting",
    message: "Generating report...",
  });
  sendProgress({
    step: 5,
    progress: 100,
    status: "complete",
    message: "Report generated.",
  });

  return finalReport;
}

module.exports = {
  runReferenceFilesFlow,
};
