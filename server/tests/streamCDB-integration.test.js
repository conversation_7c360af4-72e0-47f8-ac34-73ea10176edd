// Comprehensive Integration Tests for streamCDB and Flow Files
const EventEmitter = require("events");

// Setup comprehensive mocks
jest.mock("../utils/helpers/tiktoken", () => {
  class FakeTokenManager {
    countFromString(str = "") {
      return str.split(/\s+/).filter(Boolean).length;
    }
  }
  return { TokenManager: FakeTokenManager };
});

const mockGetChatCompletion = jest.fn();
jest.doMock("../utils/helpers", () => ({
  getLLMProvider: () => ({
    compressMessages: jest.fn(async (messages) => messages),
    getChatCompletion: mockGetChatCompletion,
    defaultTemp: 0.7,
    model: "mock-model",
    metrics: { lastCompletionTokens: 0 },
    promptWindowLimit: jest.fn(() => 128000),
  }),
}));

const mockSystemSettingsGet = jest.fn();
jest.doMock("../models/systemSettings", () => ({
  SystemSettings: {
    get: mockSystemSettingsGet,
    getValueOrFallback: jest.fn(async (clause, fallback) => {
      const result = await mockSystemSettingsGet(clause);
      return result?.value ?? fallback;
    }),
  },
}));

jest.doMock("../models/workspace", () => ({
  Workspace: {
    where: jest.fn().mockResolvedValue([]),
    get: jest.fn().mockResolvedValue(null),
  },
}));

jest.doMock("../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({ chat: { id: 1 } }),
  },
}));

// Track cleanup calls for testing
const mockPurgeDocumentBuilder = jest.fn().mockReturnValue(0);
jest.doMock("../utils/files", () => ({
  purgeDocumentBuilder: mockPurgeDocumentBuilder,
}));

const mockGenerateLegalMemo = jest.fn().mockResolvedValue({
  memo: "mock memo",
  tokenCount: 5,
  sources: [],
  tokenUsage: {},
});

jest.doMock("../utils/helpers/legalMemo", () => ({
  generateLegalMemo: mockGenerateLegalMemo,
}));

// Enhanced file system mock with detailed tracking
const mockFileOperations = {
  existsSync: jest.fn().mockReturnValue(true),
  readdirSync: jest.fn().mockReturnValue([]),
  readFileSync: jest.fn(),
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
  createWriteStream: jest.fn(),
  rmSync: jest.fn(),
};

jest.doMock("fs", () => {
  const actual = jest.requireActual("fs");
  return {
    ...actual,
    ...mockFileOperations,
  };
});

const { streamChatWithWorkspaceCDB } = require("../utils/chats/streamCDB");

jest.setTimeout(20000);

describe("StreamCDB Integration Tests - File Operations & Flow Integration", () => {
  beforeEach(() => {
    // Reset all mocks
    mockGetChatCompletion.mockReset();
    mockSystemSettingsGet.mockReset();
    mockGenerateLegalMemo.mockClear();
    mockPurgeDocumentBuilder.mockClear();

    // Reset file operation mocks
    Object.values(mockFileOperations).forEach((mock) => mock.mockClear());

    // Default behavior
    mockSystemSettingsGet.mockImplementation(async ({ label }) =>
      Promise.resolve(null)
    );
    mockGetChatCompletion.mockImplementation(async (messages) => ({
      textResponse: "Fallback LLM response",
      metrics: { lastCompletionTokens: 5 },
    }));

    // Default file system behavior
    mockFileOperations.readFileSync.mockImplementation((filePath) => {
      if (filePath.includes("final-document-") && filePath.endsWith(".md")) {
        return `## Legal Analysis

This section provides a comprehensive legal analysis based on the available documentation. The analysis covers key legal principles, applicable statutes, and relevant case law that inform the legal position.

### Key Findings

The documentation review reveals several important legal considerations that must be addressed in this matter.

## Conclusions

Based on the legal analysis presented above, the following conclusions and recommendations are provided:

1. The legal framework supports the proposed approach
2. Certain compliance requirements must be satisfied
3. Risk mitigation strategies should be implemented

### Recommendations

It is recommended that the following steps be taken to ensure full legal compliance and minimize potential risks.`;
      }
      return JSON.stringify({
        pageContent: "dummy content for readFileSync mock",
        token_count_estimate: 10,
        metadata: { title: "dummyTitleFromReadFileSync" },
      });
    });

    // Mock stream for file writing
    const mockStream = {
      write: jest.fn(),
      end: jest.fn(),
      on: jest.fn((event, callback) => {
        if (event === "finish") {
          setTimeout(callback, 10);
        }
      }),
    };
    mockFileOperations.createWriteStream.mockReturnValue(mockStream);
  });

  describe("File Creation and Management Tests", () => {
    test("Main Flow - Complete file lifecycle with proper naming and cleanup", async () => {
      const request = new EventEmitter();
      const chunks = [];
      const response = {
        write: (data) => {
          const strData = data.toString();
          const cleanedStr = strData.startsWith("data: ")
            ? strData.substring(6).trim()
            : strData.trim();
          if (
            cleanedStr &&
            cleanedStr.startsWith("{") &&
            cleanedStr.endsWith("}")
          ) {
            try {
              chunks.push(JSON.parse(cleanedStr));
            } catch (e) {}
          }
        },
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 1,
        slug: "main-flow-files",
        type: "document-drafting",
      };
      const mockChatId = "main-file-test-123";
      const mockCdbOptions = [
        "Main Flow File Test",
        "Instructions",
        "mainDoc.pdf",
      ];
      const mockLegalTaskConfig = { flowType: "main" };
      const MOCK_FILES = ["doc1.json", "mainDoc.pdf.json"];

      mockFileOperations.readdirSync.mockReturnValue(MOCK_FILES);

      // Enhanced LLM setup for main flow - more comprehensive mocking
      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            { index_number: 1, title: "Main Section 1" },
          ]),
          metrics: { lastCompletionTokens: 20 },
        })
        .mockResolvedValueOnce({
          textResponse: "Description for doc1",
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "Description for mainDoc",
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "true",
          metrics: { lastCompletionTokens: 5 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([1]),
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Legal Issue A" }]),
          metrics: { lastCompletionTokens: 20 },
        })
        .mockResolvedValueOnce({
          textResponse: "## Main Section 1\n\nDrafted content...",
          metrics: { lastCompletionTokens: 50 },
        });

      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "Legal memo content",
        tokenCount: 100,
        sources: [],
      });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Main Flow File Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Debug: Log what files were actually written
      const writeFileSyncCalls = mockFileOperations.writeFileSync.mock.calls;
      expect(writeFileSyncCalls.length).toBeGreaterThan(0);

      // Check for any files that contain the chatId (more flexible check)
      const filesWithChatId = writeFileSyncCalls.filter((call) =>
        call[0].includes(mockChatId)
      );
      expect(filesWithChatId.length).toBeGreaterThan(0);

      // Verify all files contain chatId and are in correct directory
      writeFileSyncCalls.forEach(([filePath, content]) => {
        expect(filePath).toMatch(
          new RegExp(
            `.*[/\\\\]storage[/\\\\]document-builder[/\\\\].*${mockChatId}.*`
          )
        );
        expect(typeof content).toBe("string");
        expect(content.length).toBeGreaterThan(0);
      });

      // Verify final document content was delivered (updated to look for textResponse type)
      const documentChunk = chunks.find(
        (c) =>
          c.type === "textResponse" &&
          c.textResponse &&
          c.textResponse.length > 100 &&
          c.close === true
      );
      expect(documentChunk).toBeDefined();
      expect(documentChunk.textResponse).toContain("Legal Analysis");
      expect(documentChunk.textResponse).toContain("Conclusions");

      // Verify cleanup occurred
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({
        uuid: mockChatId,
      });
    });

    test("NoMain Flow - File creation without main document", async () => {
      const request = new EventEmitter();
      const response = {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 2,
        slug: "nomain-flow-files",
        type: "document-drafting",
      };
      const mockChatId = "nomain-file-test-456";
      const mockCdbOptions = ["NoMain Flow Test", "Instructions", null];
      const mockLegalTaskConfig = { flowType: "noMain" };
      const MOCK_FILES = ["doc1.json", "doc2.json"];

      mockFileOperations.readdirSync.mockReturnValue(MOCK_FILES);

      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: "Description 1",
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: "Description 2",
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            {
              index_number: 1,
              title: "Section 1",
              relevant_documents: ["doc1"],
            },
          ]),
          metrics: { lastCompletionTokens: 20 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Issue 1" }]),
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "Drafted section content",
          metrics: { lastCompletionTokens: 30 },
        });

      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "Memo content",
        tokenCount: 50,
        sources: [],
      });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "NoMain Flow Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Debug: Log what files were actually written
      const writeFileSyncCalls = mockFileOperations.writeFileSync.mock.calls;
      expect(writeFileSyncCalls.length).toBeGreaterThan(0);

      // Check that files with chatId were created
      const filesWithChatId = writeFileSyncCalls.filter((call) =>
        call[0].includes(mockChatId)
      );
      expect(filesWithChatId.length).toBeGreaterThan(0);

      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({
        uuid: mockChatId,
      });
    });

    test("Reference Flow - Compliance analysis file management", async () => {
      const request = new EventEmitter();
      const response = {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 3,
        slug: "ref-flow-files",
        type: "document-drafting",
      };
      const mockChatId = "ref-file-test-789";
      const referenceFiles = ["policy.json", "guidelines.json"];
      const allFiles = ["policy.json", "guidelines.json", "contract.json"];

      const mockCdbOptions = [
        "Reference Flow Test",
        "Instructions",
        null,
        "referenceFiles",
        referenceFiles,
      ];
      const mockLegalTaskConfig = { flowType: "referenceFiles" };

      mockFileOperations.readdirSync.mockReturnValue(allFiles);

      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: "Policy description",
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "Guidelines description",
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "Contract description",
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            {
              index_number: 1,
              title: "Compliance Check",
              relevant_documents: ["policy.json"],
            },
          ]),
          metrics: { lastCompletionTokens: 25 },
        })
        .mockResolvedValueOnce({
          textResponse: "## Compliance Check\n\nCompliance analysis content...",
          metrics: { lastCompletionTokens: 40 },
        });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Reference Flow Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Debug: Log what files were actually written
      const writeFileSyncCalls = mockFileOperations.writeFileSync.mock.calls;

      // Reference flow might not write files in the same way - check if any calls were made
      // The reference flow returns content directly rather than writing intermediate files
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({
        uuid: mockChatId,
      });
    });
  });

  describe("Content Generation and Validation", () => {
    test("Section content quality and structure validation", async () => {
      const request = new EventEmitter();
      const chunks = [];
      const response = {
        write: (data) => {
          const strData = data.toString();
          const cleanedStr = strData.startsWith("data: ")
            ? strData.substring(6).trim()
            : strData.trim();
          if (
            cleanedStr &&
            cleanedStr.startsWith("{") &&
            cleanedStr.endsWith("}")
          ) {
            try {
              chunks.push(JSON.parse(cleanedStr));
            } catch (e) {}
          }
        },
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 4,
        slug: "content-quality",
        type: "document-drafting",
      };
      const mockChatId = "content-test-abc";
      const mockCdbOptions = ["Quality Test", "Detailed analysis", null];
      const mockLegalTaskConfig = { flowType: "noMain" };

      mockFileOperations.readdirSync.mockReturnValue(["quality-doc.json"]);

      const qualityContent = {
        description:
          "Comprehensive legal analysis document with detailed regulatory framework examination.",
        sectionContent:
          "## Regulatory Analysis\n\nThis section provides comprehensive analysis...\n\n### Key Components\n\n1. Primary requirements\n2. Secondary guidance\n3. Industry standards\n\n### Implementation Strategy\n\nDetailed implementation approach...",
      };

      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: qualityContent.description,
          metrics: { lastCompletionTokens: 25 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            {
              index_number: 1,
              title: "Regulatory Analysis",
              relevant_documents: ["quality-doc"],
              description: "Analysis section",
            },
          ]),
          metrics: { lastCompletionTokens: 30 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Regulatory complexity" }]),
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: qualityContent.sectionContent,
          metrics: { lastCompletionTokens: 80 },
        });

      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "Detailed regulatory analysis memo with citations and recommendations.",
        tokenCount: 120,
        sources: ["reg-1", "case-2"],
      });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Quality Analysis Task",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Verify content quality (updated to look for textResponse type)
      const documentChunk = chunks.find(
        (c) =>
          c.type === "textResponse" &&
          c.textResponse &&
          c.textResponse.length > 100 &&
          c.close === true
      );
      expect(documentChunk).toBeDefined();

      const content = documentChunk.textResponse;
      expect(content).toContain("## Regulatory Analysis");
      expect(content).toContain("### Key Components");
      expect(content).toContain("1. Primary requirements");
      expect(content).toContain("### Implementation Strategy");
      expect(content.length).toBeGreaterThan(200);
    });

    test("Multi-section document generation and combination", async () => {
      const request = new EventEmitter();
      const chunks = [];
      const response = {
        write: (data) => {
          const strData = data.toString();
          const cleanedStr = strData.startsWith("data: ")
            ? strData.substring(6).trim()
            : strData.trim();
          if (
            cleanedStr &&
            cleanedStr.startsWith("{") &&
            cleanedStr.endsWith("}")
          ) {
            try {
              chunks.push(JSON.parse(cleanedStr));
            } catch (e) {}
          }
        },
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 5,
        slug: "multi-section",
        type: "document-drafting",
      };
      const mockChatId = "multi-test-def";
      const mockCdbOptions = ["Multi-Section Test", "", null];
      const mockLegalTaskConfig = { flowType: "noMain" };

      mockFileOperations.readdirSync.mockReturnValue([
        "doc1.json",
        "doc2.json",
      ]);

      // Mock responses for multi-section document
      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: "Document 1 description",
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: "Document 2 description",
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            {
              index_number: 1,
              title: "Introduction",
              relevant_documents: ["doc1"],
              description: "Intro section",
            },
            {
              index_number: 2,
              title: "Analysis",
              relevant_documents: ["doc2"],
              description: "Analysis section",
            },
            {
              index_number: 3,
              title: "Conclusion",
              relevant_documents: ["doc1", "doc2"],
              description: "Conclusion section",
            },
          ]),
          metrics: { lastCompletionTokens: 40 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Issue 1" }]),
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Issue 2" }]),
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Issue 3" }]),
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: "## Introduction\n\nThis document introduces...",
          metrics: { lastCompletionTokens: 30 },
        })
        .mockResolvedValueOnce({
          textResponse: "## Analysis\n\nDetailed analysis reveals...",
          metrics: { lastCompletionTokens: 35 },
        })
        .mockResolvedValueOnce({
          textResponse:
            "## Conclusion\n\nIn conclusion, the findings indicate...",
          metrics: { lastCompletionTokens: 40 },
        });

      mockGenerateLegalMemo
        .mockResolvedValueOnce({ memo: "Memo 1", tokenCount: 50, sources: [] })
        .mockResolvedValueOnce({ memo: "Memo 2", tokenCount: 60, sources: [] })
        .mockResolvedValueOnce({ memo: "Memo 3", tokenCount: 55, sources: [] });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Multi-Section Document",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Verify multi-section content (updated to look for textResponse type)
      const documentChunk = chunks.find(
        (c) =>
          c.type === "textResponse" &&
          c.textResponse &&
          c.textResponse.length > 100 &&
          c.close === true
      );
      expect(documentChunk).toBeDefined();

      const content = documentChunk.textResponse;
      expect(content).toContain("## Introduction");
      expect(content).toContain("## Analysis");
      expect(content).toContain("## Conclusion");

      // Verify sections are properly combined
      const introIndex = content.indexOf("## Introduction");
      const analysisIndex = content.indexOf("## Analysis");
      const conclusionIndex = content.indexOf("## Conclusion");

      expect(introIndex).toBeLessThan(analysisIndex);
      expect(analysisIndex).toBeLessThan(conclusionIndex);
    });
  });

  describe("Cleanup and Error Handling", () => {
    test("Proper cleanup after successful completion", async () => {
      const request = new EventEmitter();
      const response = {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 6,
        slug: "cleanup-success",
        type: "document-drafting",
      };
      const mockChatId = "cleanup-success-123";
      const mockCdbOptions = ["Cleanup Test", "", null];
      const mockLegalTaskConfig = { flowType: "noMain" };

      mockFileOperations.readdirSync.mockReturnValue(["test.json"]);

      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: "Test description",
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            { index_number: 1, title: "Test", relevant_documents: ["test"] },
          ]),
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Test issue" }]),
          metrics: { lastCompletionTokens: 8 },
        })
        .mockResolvedValueOnce({
          textResponse: "Test content",
          metrics: { lastCompletionTokens: 20 },
        });

      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "Test memo",
        tokenCount: 30,
        sources: [],
      });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Cleanup Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Verify cleanup was called exactly once
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledTimes(1);
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({
        uuid: mockChatId,
      });
    });

    test("Cleanup occurs even when file operations fail", async () => {
      const request = new EventEmitter();
      const response = {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 7,
        slug: "cleanup-error",
        type: "document-drafting",
      };
      const mockChatId = "cleanup-error-456";
      const mockCdbOptions = ["Error Test", "", null];
      const mockLegalTaskConfig = { flowType: "noMain" };

      mockFileOperations.readdirSync.mockReturnValue(["test.json"]);

      // Simulate file write error
      mockFileOperations.writeFileSync.mockImplementationOnce(() => {
        throw new Error("File write error");
      });

      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: "Test description",
          metrics: { lastCompletionTokens: 10 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            { index_number: 1, title: "Test", relevant_documents: ["test"] },
          ]),
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Test issue" }]),
          metrics: { lastCompletionTokens: 8 },
        })
        .mockResolvedValueOnce({
          textResponse: "Test content",
          metrics: { lastCompletionTokens: 20 },
        });

      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "Test memo",
        tokenCount: 30,
        sources: [],
      });

      await expect(
        streamChatWithWorkspaceCDB(
          request,
          response,
          workspace,
          "Error Test",
          "chat",
          { id: 1, role: "admin" },
          null,
          [],
          mockChatId,
          false,
          false,
          "",
          null,
          "default",
          false,
          null,
          false,
          mockCdbOptions,
          mockLegalTaskConfig
        )
      ).resolves.not.toThrow();

      // Verify cleanup still occurred despite file error
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({
        uuid: mockChatId,
      });
    });

    test("No premature cleanup during processing", async () => {
      const request = new EventEmitter();
      const response = {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 8,
        slug: "no-premature-cleanup",
        type: "document-drafting",
      };
      const mockChatId = "no-premature-cleanup-789";
      const mockCdbOptions = ["Premature Test", "", null];
      const mockLegalTaskConfig = { flowType: "noMain" };

      mockFileOperations.readdirSync.mockReturnValue(["test.json"]);

      // Add delay to LLM responses to simulate processing time
      mockGetChatCompletion.mockImplementation(async () => {
        await new Promise((resolve) => setTimeout(resolve, 50));
        return {
          textResponse: "Delayed response",
          metrics: { lastCompletionTokens: 10 },
        };
      });

      mockGenerateLegalMemo.mockImplementation(async () => {
        await new Promise((resolve) => setTimeout(resolve, 50));
        return { memo: "Delayed memo", tokenCount: 30, sources: [] };
      });

      const startTime = Date.now();

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Premature Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Verify processing took some time (indicating no premature cleanup)
      expect(processingTime).toBeGreaterThan(100);

      // Verify cleanup happened at the end
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({
        uuid: mockChatId,
      });
    });
  });

  describe("Flow Integration Verification", () => {
    test("flowDispatcher correctly routes to mainDoc flow", async () => {
      const request = new EventEmitter();
      const response = {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 9,
        slug: "main-dispatch",
        type: "document-drafting",
      };
      const mockChatId = "main-dispatch-test";
      const mockCdbOptions = ["Main Dispatch Test", "", "mainDoc.pdf"];
      const mockLegalTaskConfig = { flowType: "main" };

      mockFileOperations.readdirSync.mockReturnValue([
        "mainDoc.pdf.json",
        "other.json",
      ]);

      // Main flow specific LLM calls
      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            { index_number: 1, title: "Main Section" },
          ]),
          metrics: { lastCompletionTokens: 20 },
        })
        .mockResolvedValueOnce({
          textResponse: "Main doc description",
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "Other doc description",
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "true",
          metrics: { lastCompletionTokens: 5 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([1]),
          metrics: { lastCompletionTokens: 8 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "Main issue" }]),
          metrics: { lastCompletionTokens: 12 },
        })
        .mockResolvedValueOnce({
          textResponse: "Main section content",
          metrics: { lastCompletionTokens: 30 },
        });

      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "Main memo",
        tokenCount: 80,
        sources: [],
      });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Main Dispatch Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Debug: Log what files were actually written
      const writeFileSyncCalls = mockFileOperations.writeFileSync.mock.calls;
      expect(writeFileSyncCalls.length).toBeGreaterThan(0);

      // Check that at least some files contain the chatId
      const filesWithChatId = writeFileSyncCalls.filter((call) =>
        call[0].includes(mockChatId)
      );
      expect(filesWithChatId.length).toBeGreaterThan(0);
    });

    test("flowDispatcher correctly routes to noMain flow", async () => {
      const request = new EventEmitter();
      const response = {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
      };

      const workspace = {
        id: 10,
        slug: "nomain-dispatch",
        type: "document-drafting",
      };
      const mockChatId = "nomain-dispatch-test";
      const mockCdbOptions = ["NoMain Dispatch Test", "", null];
      const mockLegalTaskConfig = { flowType: "noMain" };

      mockFileOperations.readdirSync.mockReturnValue([
        "doc1.json",
        "doc2.json",
      ]);

      // NoMain flow specific LLM calls
      mockGetChatCompletion
        .mockResolvedValueOnce({
          textResponse: "NoMain doc1 description",
          metrics: { lastCompletionTokens: 12 },
        })
        .mockResolvedValueOnce({
          textResponse: "NoMain doc2 description",
          metrics: { lastCompletionTokens: 12 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([
            {
              index_number: 1,
              title: "NoMain Section",
              relevant_documents: ["doc1"],
            },
          ]),
          metrics: { lastCompletionTokens: 25 },
        })
        .mockResolvedValueOnce({
          textResponse: JSON.stringify([{ Issue: "NoMain issue" }]),
          metrics: { lastCompletionTokens: 15 },
        })
        .mockResolvedValueOnce({
          textResponse: "NoMain section content",
          metrics: { lastCompletionTokens: 35 },
        });

      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "NoMain memo",
        tokenCount: 70,
        sources: [],
      });

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "NoMain Dispatch Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Debug: Log what files were actually written
      const writeFileSyncCalls = mockFileOperations.writeFileSync.mock.calls;
      expect(writeFileSyncCalls.length).toBeGreaterThan(0);

      // Check that files contain the chatId
      const filesWithChatId = writeFileSyncCalls.filter((call) =>
        call[0].includes(mockChatId)
      );
      expect(filesWithChatId.length).toBeGreaterThan(0);
    });
  });
});

describe("streamChatWithWorkspaceCDB - Frontend Compatibility Integration", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock WorkspaceChats.new for chat creation
    const { WorkspaceChats } = require("../models/workspaceChats");
    jest.spyOn(WorkspaceChats, "new").mockResolvedValue({
      chat: { id: "saved-chat-123" },
    });

    // Default LLM responses for a complete flow
    mockGetChatCompletion
      // Section list generation
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([
          {
            index_number: 1,
            title: "Legal Analysis",
            Description: "Comprehensive legal analysis",
          },
          {
            index_number: 2,
            title: "Conclusions",
            Description: "Final conclusions and recommendations",
          },
        ]),
        metrics: { lastCompletionTokens: 30 },
      })
      // Document descriptions
      .mockResolvedValueOnce({
        textResponse:
          "Document 1 contains contract details and legal provisions",
        metrics: { lastCompletionTokens: 15 },
      })
      .mockResolvedValueOnce({
        textResponse: "Document 2 provides supporting case law and precedents",
        metrics: { lastCompletionTokens: 18 },
      })
      // Relevance checks
      .mockResolvedValueOnce({
        textResponse: "true",
        metrics: { lastCompletionTokens: 1 },
      })
      .mockResolvedValueOnce({
        textResponse: "true",
        metrics: { lastCompletionTokens: 1 },
      })
      // Document mapping
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([1, 2]),
        metrics: { lastCompletionTokens: 8 },
      })
      // Legal issues identification
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([
          { Issue: "Contract interpretation complexity" },
          { Issue: "Liability assessment requirements" },
        ]),
        metrics: { lastCompletionTokens: 25 },
      })
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([
          { Issue: "Damages calculation methodology" },
        ]),
        metrics: { lastCompletionTokens: 20 },
      })
      // Section drafting
      .mockResolvedValueOnce({
        textResponse:
          "## Legal Analysis\n\nThis section provides a comprehensive legal analysis based on the available documentation. The analysis covers key legal principles, applicable statutes, and relevant case law that inform the legal position.\n\n### Key Findings\n\nThe documentation review reveals several important legal considerations that must be addressed in this matter.",
        metrics: { lastCompletionTokens: 120 },
      })
      .mockResolvedValueOnce({
        textResponse:
          "## Conclusions\n\nBased on the legal analysis presented above, the following conclusions and recommendations are provided:\n\n1. The legal framework supports the proposed approach\n2. Certain compliance requirements must be satisfied\n3. Risk mitigation strategies should be implemented\n\n### Recommendations\n\nIt is recommended that the following steps be taken to ensure full legal compliance and minimize potential risks.",
        metrics: { lastCompletionTokens: 100 },
      });
  });

  test("should send final document content with frontend-compatible message type", async () => {
    const request = new EventEmitter();
    const capturedMessages = [];

    const response = {
      write: (data) => {
        const strData = data.toString();
        const cleanedStr = strData.startsWith("data: ")
          ? strData.substring(6).trim()
          : strData.trim();
        if (cleanedStr) {
          try {
            if (cleanedStr.startsWith("{") && cleanedStr.endsWith("}")) {
              const parsedMessage = JSON.parse(cleanedStr);
              capturedMessages.push(parsedMessage);
            }
          } catch (e) {
            // Ignore malformed JSON
          }
        }
      },
      on: jest.fn(),
      removeListener: jest.fn(),
    };

    const workspace = {
      id: 1,
      slug: "frontend-test-workspace",
      type: "document-drafting",
    };
    const chatId = "frontend-compatibility-test-123";
    const cdbOptions = [
      "Frontend Compatibility Test",
      "Test custom instructions",
      "main-doc.json",
    ];
    const legalTaskConfig = { flowType: "main", name: "Frontend Test Task" };

    await streamChatWithWorkspaceCDB(
      request,
      response,
      workspace,
      "Test legal analysis request",
      "chat",
      { id: 1, role: "admin" },
      null,
      [],
      chatId,
      false,
      false,
      "",
      null,
      "default",
      false,
      null,
      false,
      cdbOptions,
      legalTaskConfig
    );

    // Filter messages to find the final document content
    const finalDocumentMessage = capturedMessages.find(
      (msg) =>
        msg.textResponse &&
        msg.textResponse.length > 200 && // Substantial content
        msg.type === "textResponse" &&
        msg.close === true && // Updated: final document now has close: true
        !msg.error
    );

    // CRITICAL ASSERTION: This would have caught the original bug
    expect(finalDocumentMessage).toBeDefined();
    expect(finalDocumentMessage.type).toBe("textResponse");
    expect(finalDocumentMessage.type).not.toBe("textResponseChunk");

    // Verify the content structure
    expect(finalDocumentMessage.textResponse).toContain("## Legal Analysis");
    expect(finalDocumentMessage.textResponse).toContain("## Conclusions");
    expect(finalDocumentMessage.textResponse.length).toBeGreaterThan(500);

    // Verify the document has the expected properties for frontend compatibility
    expect(finalDocumentMessage.close).toBe(true);
    expect(finalDocumentMessage.sources).toBeDefined();
    expect(finalDocumentMessage.chatId).toBeDefined();

    // Verify progress events were sent
    const progressMessages = capturedMessages.filter(
      (msg) => msg.type === "cdbProgress"
    );
    expect(progressMessages.length).toBeGreaterThan(0);

    // Log for debugging
    console.log(
      "[INTEGRATION TEST] Final document message type:",
      finalDocumentMessage.type
    );
    console.log(
      "[INTEGRATION TEST] Content length:",
      finalDocumentMessage.textResponse.length
    );
    console.log(
      "[INTEGRATION TEST] Total messages captured:",
      capturedMessages.length
    );
  });

  test("should handle textResponseChunk vs textResponse differences correctly", async () => {
    // This test simulates the difference between the two message types
    // to verify that CDB uses the correct one for frontend compatibility

    const mockSetChatHistory = jest.fn();

    // Simulate frontend chat response handler (simplified)
    const simulateFrontendHandling = (message, chatHistory = []) => {
      if (message.type === "textResponse") {
        // textResponse creates a new complete message
        return [
          ...chatHistory,
          {
            uuid: message.uuid,
            content: message.textResponse,
            type: message.type,
            role: "assistant",
            closed: message.close,
            sources: message.sources || [],
          },
        ];
      }

      if (message.type === "textResponseChunk") {
        // textResponseChunk requires an existing pending message
        const chatIdx = chatHistory.findLastIndex(
          (chat) => chat.role === "assistant" && chat.pending === true
        );

        if (chatIdx === -1) {
          // No pending message - content disappears!
          console.warn(`Stream chunk received but no pending message found`);
          return chatHistory;
        }

        // Update existing pending message
        const updatedHistory = [...chatHistory];
        updatedHistory[chatIdx] = {
          ...updatedHistory[chatIdx],
          content:
            (updatedHistory[chatIdx].content || "") + message.textResponse,
          pending: false,
        };
        return updatedHistory;
      }

      return chatHistory;
    };

    // Test 1: textResponse (correct for CDB) works without pending message
    const textResponseMessage = {
      uuid: "test-uuid-1",
      type: "textResponse",
      textResponse: "Complete CDB document content",
      close: false,
      sources: [],
    };

    const historyAfterTextResponse = simulateFrontendHandling(
      textResponseMessage,
      []
    );
    expect(historyAfterTextResponse).toHaveLength(1);
    expect(historyAfterTextResponse[0].content).toBe(
      "Complete CDB document content"
    );

    // Test 2: textResponseChunk (problematic for CDB) fails without pending message
    const textResponseChunkMessage = {
      uuid: "test-uuid-2",
      type: "textResponseChunk",
      textResponse: "This content would disappear!",
      close: false,
      sources: [],
    };

    const consoleSpy = jest.spyOn(console, "warn").mockImplementation(() => {});
    const historyAfterChunk = simulateFrontendHandling(
      textResponseChunkMessage,
      []
    );

    // Content disappears because there's no pending message
    expect(historyAfterChunk).toHaveLength(0);
    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining(
        "Stream chunk received but no pending message found"
      )
    );

    consoleSpy.mockRestore();

    // Test 3: textResponseChunk works WITH pending message
    const pendingMessage = {
      uuid: "test-uuid-3",
      content: "",
      role: "assistant",
      pending: true,
    };

    const textResponseChunkWithPending = {
      uuid: "test-uuid-3",
      type: "textResponseChunk",
      textResponse: "This content attaches correctly",
      close: false,
      sources: [],
    };

    const historyWithPending = simulateFrontendHandling(
      textResponseChunkWithPending,
      [pendingMessage]
    );

    expect(historyWithPending).toHaveLength(1);
    expect(historyWithPending[0].content).toBe(
      "This content attaches correctly"
    );
    expect(historyWithPending[0].pending).toBe(false);
  });
});
