import useRexorStore from "../rexorStore";
import {
  getLatestEstimationForThread,
  initializeManualWorkEstimation,
} from "../estimationStore";
import { writeArticleTransaction } from "@/services/rexorService";
import { act } from "react-dom/test-utils";

// Mock dependencies
jest.mock("../estimationStore", () => ({
  ...jest.requireActual("../estimationStore"),
  getLatestEstimationForThread: jest.fn(),
}));
jest.mock("@/services/rexorService");
jest.mock("i18next", () => ({
  t: (key, options) => {
    if (key === "rexor.estimated-manual-time" && options?.hours) {
      return ` * Estimated manual time: ${options.hours} hours`;
    }
    if (key === "rexor.invoice-text") {
      return "Default invoice text";
    }
    return key;
  },
}));

// Mock window.location
const mockLocation = {
  pathname: "/workspace/test-ws/thread/test-thread",
};
Object.defineProperty(window, "location", {
  value: mockLocation,
  writable: true,
});

describe("rexorStore - writeNewArticleTransaction", () => {
  const mockGetLatestEstimation = getLatestEstimationForThread;

  beforeEach(() => {
    // Reset mocks and store state before each test
    jest.clearAllMocks();
    act(() => {
      useRexorStore.setState({ token: "test-token" }, true);
    });
    mockGetLatestEstimation.mockReturnValue(null);
    writeArticleTransaction.mockResolvedValue({
      success: true,
      UID: "new-txn",
    });
  });

  it("should append estimation to invoice text if an estimation is found", async () => {
    mockGetLatestEstimation.mockReturnValue({ totalHours: 2.5 });
    const transactionData = { InvoiceText: "Legal research" };

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData);
    });

    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "Legal research * Estimated manual time: 2.5 hours",
      }),
      "test-token"
    );
  });

  it("should use original invoice text when no estimation is found", async () => {
    const transactionData = { InvoiceText: "Client meeting" };

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData);
    });

    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "Client meeting",
      }),
      "test-token"
    );
  });

  it("should handle estimation retrieval errors gracefully", async () => {
    mockGetLatestEstimation.mockImplementation(() => {
      throw new Error("Failed to get estimation");
    });
    const transactionData = { InvoiceText: "Document review" };

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData);
    });

    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "Document review",
      }),
      "test-token"
    );
  });

  it("should not add estimation text if totalHours is 0 or less", async () => {
    mockGetLatestEstimation.mockReturnValue({ totalHours: 0 });
    const transactionData = { InvoiceText: "Quick check" };

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData);
    });

    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "Quick check",
      }),
      "test-token"
    );
  });

  it("should correctly parse thread slug from URL", async () => {
    mockLocation.pathname = "/workspace/my-ws/thread/my-thread-123";
    mockGetLatestEstimation.mockReturnValue({ totalHours: 1.5 });
    const transactionData = { InvoiceText: "Analysis" };

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData);
    });

    expect(mockGetLatestEstimation).toHaveBeenCalledWith("my-thread-123");
    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "Analysis * Estimated manual time: 1.5 hours",
      }),
      "test-token"
    );
  });

  it("should not look for estimation if URL does not match thread pattern", async () => {
    mockLocation.pathname = "/dashboard";
    const transactionData = { InvoiceText: "General work" };

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData);
    });

    expect(mockGetLatestEstimation).not.toHaveBeenCalled();
    expect(writeArticleTransaction).toHaveBeenCalledWith(
      expect.objectContaining({
        InvoiceText: "General work",
      }),
      "test-token"
    );
  });

  it("should update the store with the new transaction UID on success", async () => {
    const transactionData = { InvoiceText: "Finalizing report" };
    expect(useRexorStore.getState().transactionUID).toBeNull();

    await act(async () => {
      await useRexorStore
        .getState()
        .writeNewArticleTransaction(transactionData);
    });

    expect(useRexorStore.getState().transactionUID).toBe("new-txn");
  });
});
