import { useState, useEffect, useRef } from "react";
import Cha<PERSON><PERSON><PERSON><PERSON> from "./ChatHistory";
import PromptInput, { PROMPT_INPUT_EVENT } from "./PromptInput";
import Workspace from "@/models/workspace";
import { handleChatResponse } from "@/utils/chat";
import { useParams } from "react-router-dom";
import { v4 } from "uuid";
import AttachmentWrapper from "./Attachments";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import {
  useClearAttachments,
  useThreadAttachments,
} from "@/stores/attachmentStore";
import { useRexorActiveReference } from "@/stores/rexorStore";
import { useAttachmentUploader } from "@/hooks/useAttachmentUploader";
import { useTranslation } from "react-i18next";
import PromptSuggestions from "./PromptSuggestions";
import ExamplePromptSubmitter from "./ExamplePromptSubmitter";
import {
  formatAttachmentsFor<PERSON>pi,
  createDisplayMessage,
} from "@/utils/attachments";
import { useIsDocumentDrafting } from "@/stores/userStore";
import useProgressStore from "@/stores/progressStore";
import useThreadProgress from "@/hooks/useThreadProgress";

function createUserMessage(content, chatId, attachments) {
  return {
    content,
    role: "user",
    chatId,
    attachments,
  };
}

function createAssistantMessage(
  userMessage,
  attachments,
  existingChatId = null
) {
  const uuid = existingChatId || v4();
  return {
    uuid,
    content: "",
    role: "assistant",
    pending: true,
    userMessage,
    animate: true,
    sources: [],
    attachments,
  };
}

export default function ChatContainer({ workspace, knownHistory = [] }) {
  const { threadSlug = "" } = useParams();
  const [message, setMessage] = useState("");
  const [loadingResponse, setLoadingResponse] = useState(false);
  const [chatHistory, setChatHistory] = useState(knownHistory);
  const invoice_ref = useRexorActiveReference();
  const [isDragging, setIsDragging] = useState(false);
  const [useDeepSearch, setUseDeepSearch] = useState(false);
  const abortControllerRef = useRef(null);
  const chatContainerRef = useRef(null);
  const { t } = useTranslation();
  const { isUploading, handleFiles } = useAttachmentUploader();
  const attachments = useThreadAttachments(threadSlug);
  const clearAttachmentsStore = useClearAttachments();
  const emptyChat = !chatHistory.length;

  const progress = useThreadProgress(threadSlug);
  const { startProcess } = useProgressStore();
  const isDocumentDrafting = useIsDocumentDrafting();

  useEffect(() => {
    if (progress.stepStatus === "error" && !progress.isActive) {
      setLoadingResponse(false);
    }
  }, [progress.stepStatus, progress.isActive]);

  const handleMessageChange = (event, value) => {
    if (value !== undefined) {
      setMessage(value);
    } else if (event.target.value !== undefined && event.target.value !== "") {
      setMessage(event.target.value);
    }
  };

  const { listening, resetTranscript } = useSpeechRecognition({
    clearTranscriptOnListen: true,
  });

  const handleSubmit = async (event, options = {}) => {
    if (event?.preventDefault) {
      event.preventDefault();
    }

    if (isDocumentDrafting && !options.cdb) {
      startProcess("document-drafting", threadSlug, 3, "documentDrafting");
    }

    const isEvent = typeof event === "object" && event !== null && event.target;
    const promptToSend = isEvent ? message : event || message;
    const messageToSend = promptToSend;
    const messageToDisplay = options.displayMessage || promptToSend;

    if (!promptToSend || promptToSend.trim() === "") {
      return false;
    }

    const attachmentsList = formatAttachmentsForApi(attachments, t);
    const displayMessageWithAttachments = createDisplayMessage(
      messageToDisplay,
      attachmentsList
    );

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    const controller = new AbortController();
    abortControllerRef.current = controller;
    clearAttachmentsStore(threadSlug);

    let baseHistory = chatHistory;
    if (progress.error) {
      const cancellationMessage = {
        uuid: `cancelled-${Date.now()}`,
        type: "statusResponse",
        content: t("chatProgress.cancelled"),
        role: "assistant",
        closed: true,
        error: null,
        animate: false,
        pending: false,
      };

      baseHistory = [...chatHistory, cancellationMessage];

      progress.clearError();
    }

    let prevChatHistory;
    let assistantMessageUuid;

    if (options.skipUserMessage && options.existingChatId) {
      prevChatHistory = baseHistory;
      assistantMessageUuid = options.existingChatId;
    } else {
      const assistantMessage = createAssistantMessage(
        messageToSend,
        attachmentsList
      );
      const userMessage = createUserMessage(
        displayMessageWithAttachments,
        assistantMessage.uuid,
        attachmentsList
      );

      prevChatHistory = [...baseHistory, userMessage, assistantMessage];
      assistantMessageUuid = assistantMessage.uuid;

      setChatHistory(prevChatHistory);
    }

    handleMessageChange(null, "");
    setLoadingResponse(true);

    if (listening) {
      endTTSSession();
    }

    try {
      await Workspace.multiplexStream({
        workspaceSlug: workspace.slug,
        threadSlug,
        prompt: messageToSend,
        chatHandler: (chatResult) => {
          handleChatResponse(
            chatResult,
            setLoadingResponse,
            setChatHistory,
            null,
            threadSlug
          );
        },
        attachments: attachmentsList,
        invoice_ref,
        chatId: assistantMessageUuid,
        abortController: controller,
        displayMessage: options.displayMessage,
        useDeepSearch: useDeepSearch,
        settingsSuffix: options.settingsSuffix || "",
      });
    } catch (error) {
      console.error("Error in handleSubmit stream:", error);
      setChatHistory((prev) => {
        const updatedHistory = prev.map((msg) => {
          if (msg.uuid === assistantMessageUuid) {
            return {
              ...msg,
              error: error.message || "An error occurred.",
              pending: false,
              animate: false,
              closed: true,
            };
          }
          return msg;
        });
        return updatedHistory;
      });
      setLoadingResponse(false);
    } finally {
      if (abortControllerRef.current === controller) {
        abortControllerRef.current = null;
      }
    }
  };

  function endTTSSession() {
    SpeechRecognition.stopListening();
    resetTranscript();
  }

  const regenerateAssistantMessage = (chatId) => {
    const messageIndex = chatHistory.findIndex(
      (msg) => msg.chatId === chatId && msg.role === "assistant"
    );

    if (messageIndex === -1) {
      console.error("Could not find message with chatId:", chatId);
      return;
    }

    const userMessageIndex = messageIndex - 1;

    if (userMessageIndex < 0 || chatHistory[userMessageIndex].role !== "user") {
      console.error("Could not find corresponding user message");
      return;
    }

    const userMessage = chatHistory[userMessageIndex];
    const assistantMessage = chatHistory[messageIndex];
    const updatedHistory = chatHistory.slice(0, messageIndex);

    setChatHistory([
      ...updatedHistory,
      createAssistantMessage(
        userMessage.content,
        userMessage.attachments,
        chatId
      ),
    ]);

    setLoadingResponse(true);

    Workspace.multiplexStream({
      workspaceSlug: workspace.slug,
      threadSlug,
      prompt: userMessage.content,
      chatId: chatId,
      invoice_ref,
      chatHandler: (chatResult) =>
        handleChatResponse(
          chatResult,
          setLoadingResponse,
          setChatHistory,
          updatedHistory,
          threadSlug
        ),
      attachments: userMessage?.attachments || [],
      isCanvasChat: false,
      preventChatCreation: true,
      cdb: false,
      abortController: null,
      useDeepSearch: useDeepSearch,
      displayMessage: null,
      settingsSuffix: "",
    }).catch((e) => {
      console.error("Error regenerating message:", e);
      setChatHistory((prev) =>
        prev.map((msg) =>
          msg.uuid === chatId && msg.pending
            ? {
                ...msg,
                error: e.message || "Failed to regenerate response.",
                pending: false,
                animate: false,
                closed: true,
              }
            : msg
        )
      );
      setLoadingResponse(false);
    });
  };

  /**
   * Send a command to the LLM prompt input.
   * @param {string} command - The command to send to the LLM
   * @param {boolean} submit - Whether the command was submitted (default: false)
   * @param {Object[]} history - The history of the chat
   * @param {Object[]} attachments - The attachments to send to the LLM
   * @returns {boolean} - Whether the command was sent successfully
   * Priority for attachments is handled by reducing the available context window
   * by the total number of tokens in all attachments. This ensures that:
   * 1. Attachments are always included first
   * 2. Remaining tokens are available for Dynamic PDR vector search
   * 3. A minimum context window is maintained for coherent responses
   */
  const sendCommand = async (
    command,
    submit = false,
    history = [],
    attachments = [],
    options = {
      chatHandler: null,
      chatId: null,
      preventNewChat: false,
      isCanvasChat: false,
      preventChatCreation: true,
      cdb: false,
      abortController: null,
      displayMessage: null,
      cdbOptions: [],
    }
  ) => {
    if (!command || command === "") return false;
    if (!submit) {
      handleMessageChange(null, command);
      window.dispatchEvent(
        new CustomEvent(PROMPT_INPUT_EVENT, { detail: command })
      );
      return;
    }

    if (loadingResponse) return;
    setLoadingResponse(true);

    const abortController = options.abortController || new AbortController();
    if (!options.abortController) {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = abortController;
    }

    let response = "";
    if (options.preventNewChat) {
      // Add user message to chat history so user can see what was sent
      const commandWithAttachments =
        attachments.length > 0
          ? `${command}\n\n${attachments.map((att) => att.contentString).join("\n\n")}`
          : command;

      // Use displayMessage if provided, otherwise use the command
      const displayText = options.displayMessage || commandWithAttachments;

      const newUserMessage = {
        content: displayText,
        role: "user",
        uuid: v4(), // Proper UUID for the message
      };

      // Add the user message to chat history
      setChatHistory((prevHistory) => [...prevHistory, newUserMessage]);

      setLoadingResponse(true);
      let resolveCloseSignal;
      const closeSignalPromise = new Promise((resolve) => {
        resolveCloseSignal = resolve;
      });

      try {
        const multiplexStreamPromise = Workspace.multiplexStream({
          workspaceSlug: workspace.slug,
          threadSlug,
          prompt: command,
          chatId: options.chatId,
          invoice_ref,
          chatHandler: (chatResult) => {
            response += chatResult.textResponse || chatResult.response || "";
            if (options.chatHandler) {
              options.chatHandler(chatResult);
            }
            if (chatResult?.close === true) {
              resolveCloseSignal();
            }
          },
          isCanvasChat: options.isCanvasChat,
          preventChatCreation: options.preventChatCreation,
          cdb: options.cdb,
          cdbOptions: options.cdbOptions,
          abortController,
          useDeepSearch: useDeepSearch,
          displayMessage: options.displayMessage,
          settingsSuffix: options.settingsSuffix || "",
        });

        await Promise.all([multiplexStreamPromise, closeSignalPromise]);

        setLoadingResponse(false);

        // Add final response to chat history
        if (response.trim()) {
          if (options.chatId) {
            // Update existing message with chatId
            const updatedHistory = [...history];
            const targetIdx = history.findIndex(
              (msg) => msg.chatId === options.chatId && msg.role === "assistant"
            );
            if (targetIdx >= 0) {
              updatedHistory[targetIdx].content = response.trim();
              setChatHistory(updatedHistory);
            }
          } else {
            // Add new assistant message for final response (like CDB)
            const finalMessage = {
              content: response.trim(),
              role: "assistant",
              uuid: v4(),
              closed: true,
              animate: false,
              pending: false,
              sources: [],
            };
            setChatHistory((prevHistory) => [...prevHistory, finalMessage]);
          }
        }
      } catch (error) {
        if (error.name === "AbortError") {
          // Silent abort handling
        } else {
          console.error("Error in sendCommand:", error);
        }
        setLoadingResponse(false);
        throw error;
      } finally {
        setLoadingResponse(false);
        if (
          !options.abortController &&
          abortControllerRef.current === abortController
        ) {
          abortControllerRef.current = null;
        }
      }

      return { text: response.trim() };
    }

    const commandWithAttachments =
      attachments.length > 0
        ? `${command}\n\n${attachments.map((att) => att.contentString).join("\n\n")}`
        : command;

    let prevChatHistory;
    if (history.length > 0) {
      prevChatHistory = [
        ...history,
        {
          content: "",
          role: "assistant",
          pending: true,
          userMessage: commandWithAttachments,
          animate: true,
        },
      ];
    } else {
      prevChatHistory = [
        ...chatHistory,
        {
          content: commandWithAttachments,
          role: "user",
        },
      ];
    }

    setChatHistory(prevChatHistory);
    handleMessageChange(null, "");
    setLoadingResponse(true);

    try {
      if (options.preventNewChat) {
        let response = "";
        await Workspace.multiplexStream({
          workspaceSlug: workspace.slug,
          threadSlug,
          prompt: commandWithAttachments,
          chatId: options.chatId,
          invoice_ref,
          chatHandler: (chatResult) => {
            response += chatResult.textResponse || chatResult.response || "";
          },
          isCanvasChat: options.isCanvasChat,
          preventChatCreation: options.preventChatCreation,
          cdb: options.cdb,
          cdbOptions: options.cdbOptions,
          useDeepSearch: useDeepSearch,
          displayMessage: options.displayMessage,
          settingsSuffix: options.settingsSuffix || "",
        });
        setLoadingResponse(false);
        return { text: response.trim() };
      } else {
        await Workspace.multiplexStream({
          workspaceSlug: workspace.slug,
          threadSlug,
          prompt: commandWithAttachments,
          invoice_ref,
          chatHandler: (chatResult) =>
            handleChatResponse(
              chatResult,
              setLoadingResponse,
              setChatHistory,
              history.length > 0 ? history : chatHistory.slice(0, -2),
              threadSlug
            ),
          isCanvasChat: options.isCanvasChat,
          preventChatCreation: options.preventChatCreation,
          cdb: options.cdb,
          cdbOptions: options.cdbOptions,
          abortController: options.abortController,
          useDeepSearch: useDeepSearch,
          displayMessage: options.displayMessage,
          settingsSuffix: options.settingsSuffix || "",
        });
      }
    } catch (error) {
      console.error("Error in sendCommand:", error);

      if (options.cdb && threadSlug) {
        progress.cancel();
      }

      setChatHistory((prev) => {
        const lastPendingIndex = prev.findLastIndex(
          (msg) => msg.role === "assistant" && msg.pending
        );
        if (lastPendingIndex === -1) return prev;

        return prev.map((msg, index) =>
          index === lastPendingIndex
            ? {
                ...msg,
                error: error.message || "An error occurred.",
                pending: false,
                animate: false,
                closed: true,
              }
            : msg
        );
      });
      setLoadingResponse(false);
      throw error;
    } finally {
      setLoadingResponse(false);
    }

    return { text: response.trim() };
  };

  // To prevent too many re-renders we remotely listen for updates from the parent
  // via an event cycle. Otherwise, using message as a prop leads to a re-render every
  // change on the input.
  function handlePromptUpdate(e) {
    const newValue = e?.detail ?? "";
    if (message === newValue) return;

    setMessage(newValue);
    window.dispatchEvent(
      new CustomEvent(PROMPT_INPUT_EVENT, { detail: newValue })
    );
  }

  useEffect(() => {
    if (window) {
      window.addEventListener(PROMPT_INPUT_EVENT, handlePromptUpdate);
    }
    return () => {
      window?.removeEventListener(PROMPT_INPUT_EVENT, handlePromptUpdate);
    };
  }, []);

  useEffect(() => {
    const chatContainerElement = chatContainerRef.current;
    if (chatContainerElement) {
      chatContainerElement.style.opacity = "1";
    }
  }, []);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const foundHistory = await Workspace.threads.chatHistory(
          workspace.slug,
          threadSlug
        );
        setChatHistory(foundHistory);
      } catch (error) {
        console.error("Error fetching initial data:", error);
        setChatHistory(knownHistory);
      }
    };

    setChatHistory([]);
    fetchInitialData();
  }, [threadSlug, workspace.slug]);

  // Function to immediately add a user message to chat history (for templates)
  const addUserMessageImmediately = (
    displayMessage,
    promptForLLM,
    attachmentsList = []
  ) => {
    const FormattedAttachments = formatAttachmentsForApi(attachmentsList, t);
    const displayMessageWithAttachments = createDisplayMessage(
      displayMessage,
      FormattedAttachments
    );
    const assistantMessage = createAssistantMessage(
      promptForLLM,
      FormattedAttachments
    );
    const userMessage = createUserMessage(
      displayMessageWithAttachments,
      assistantMessage.uuid,
      FormattedAttachments
    );

    setChatHistory((prev) => [...prev, userMessage, assistantMessage]);
    return assistantMessage.uuid;
  };

  return (
    <div
      className="relative flex flex-col w-full h-full px-6 transition-opacity duration-300 delay-200"
      style={{ opacity: 0 }}
      ref={chatContainerRef}
    >
      <ExamplePromptSubmitter
        handleSubmit={handleSubmit}
        workspace={workspace}
        chatHistory={chatHistory}
      />
      <AttachmentWrapper
        isUploading={isUploading}
        handleFiles={handleFiles}
        onDraggingChange={setIsDragging}
      >
        <ChatHistory
          history={chatHistory}
          workspace={workspace}
          sendCommand={sendCommand}
          updateHistory={setChatHistory}
          regenerateAssistantMessage={regenerateAssistantMessage}
        />

        <div className="sticky bottom-0 w-full max-w-[58rem] mx-auto">
          {emptyChat && (
            <PromptSuggestions
              suggestions={workspace?.suggestedMessages ?? []}
              handleSubmit={handleSubmit}
            />
          )}
          <PromptInput
            submit={handleSubmit}
            onChange={handleMessageChange}
            inputDisabled={false}
            buttonDisabled={loadingResponse}
            sendCommand={sendCommand}
            attachments={attachments}
            handleFiles={handleFiles}
            isDragging={isDragging}
            useDeepSearch={useDeepSearch}
            setUseDeepSearch={setUseDeepSearch}
            workspace={workspace}
            addUserMessageImmediately={addUserMessageImmediately}
          />
        </div>
      </AttachmentWrapper>
    </div>
  );
}
