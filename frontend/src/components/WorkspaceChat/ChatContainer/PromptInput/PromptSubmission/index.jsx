import React, { useEffect, useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { LuArrowRight } from "react-icons/lu";
import ReferenceWarningModal from "@/components/Modals/ReferenceWarning";
import RexorLoginModal from "@/components/Modals/RexorLoginModal";
import { validateReferenceNumberWithStore } from "@/utils/constants";
import showToast from "@/utils/toast";
import StopGenerationButton from "../StopGenerationButton";
import { SUBMIT_PROMPT_EVENT } from "../index";
import { useClearAttachments } from "@/stores/attachmentStore";
import { useInvoiceLogging } from "@/stores/settingsStore";
import {
  useRexorWriteOrUpdateTransaction,
  useRexorActiveReference,
} from "@/stores/rexorStore";

export default function PromptSubmission({
  promptInput,
  setPromptInput,
  buttonDisabled,
  submit,
  formRef,
  textareaRef,
  setShowSlashCommand,
  setShowAgents,
  threadSlug,
}) {
  const { t } = useTranslation();
  const invoiceLogging = useInvoiceLogging();
  const activeReference = useRexorActiveReference();
  const [showReferenceWarning, setShowReferenceWarning] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [pendingPrompt, setPendingPrompt] = useState("");
  const disabled = buttonDisabled || promptInput.length === 0;
  const writeOrUpdateTransaction = useRexorWriteOrUpdateTransaction();

  const hasValidReference = validateReferenceNumberWithStore(
    invoiceLogging,
    activeReference
  );

  const handleUpdateTransaction = async () => {
    await writeOrUpdateTransaction(setShowLoginModal);
  };

  const hasShownWarningForThread = useCallback((threadId) => {
    const shownWarnings = JSON.parse(
      localStorage.getItem("referenceWarnings") || "{}"
    );
    return shownWarnings[threadId];
  }, []);

  const markWarningShownForThread = useCallback((threadId) => {
    const shownWarnings = JSON.parse(
      localStorage.getItem("referenceWarnings") || "{}"
    );
    shownWarnings[threadId] = true;
    localStorage.setItem("referenceWarnings", JSON.stringify(shownWarnings));
  }, []);

  const clearAttachments = useClearAttachments();

  const handleSubmitWithReferenceCheck = useCallback(
    async (promptText) => {
      if (!hasValidReference && !hasShownWarningForThread(threadSlug)) {
        setShowReferenceWarning(true);
        setPendingPrompt(promptText);
        return;
      }

      try {
        handleUpdateTransaction();
        setPromptInput("");
        setShowSlashCommand(false);
        setShowAgents(false);
        if (textareaRef.current) {
          textareaRef.current.style.height = "auto";
        }
        // Use the Zustand store action
        clearAttachments();
        await submit(promptText);
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent("PROMPT_SENT"));
        }, 1000);
      } catch (error) {
        console.error("Error submitting prompt:", error);
      }
    },
    [
      invoiceLogging,
      hasShownWarningForThread,
      threadSlug,
      submit,
      handleUpdateTransaction,
      setPromptInput,
      setShowSlashCommand,
      setShowAgents,
      textareaRef,
      clearAttachments,
      activeReference,
      hasValidReference,
    ]
  );

  const handleProceedWithoutReference = useCallback(async () => {
    setShowReferenceWarning(false);
    if (threadSlug) {
      markWarningShownForThread(threadSlug);
    }
    if (pendingPrompt) {
      try {
        handleUpdateTransaction();
        setPromptInput("");
        setShowSlashCommand(false);
        setShowAgents(false);
        if (textareaRef.current) {
          textareaRef.current.style.height = "auto";
        }
        // Use the Zustand store action
        clearAttachments();
        await submit(pendingPrompt);
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent("PROMPT_SENT"));
        }, 1000);
      } catch (error) {
        console.error("Error submitting prompt:", error);
      }
      setPendingPrompt("");
    }
  }, [
    threadSlug,
    markWarningShownForThread,
    pendingPrompt,
    submit,
    writeOrUpdateTransaction,
    setPromptInput,
    setShowSlashCommand,
    setShowAgents,
    textareaRef,
    clearAttachments,
  ]);

  const handleSubmit = useCallback(
    (e) => {
      e?.preventDefault?.();
      if (buttonDisabled) return;

      // Force re-read the current prompt input value to ensure we have the latest
      // This is crucial for handling templates added programmatically
      const currentPromptInput = textareaRef.current?.value || promptInput;

      // Check if there's any input to submit
      if (!currentPromptInput.trim()) {
        showToast(t("prompt.error.empty"), "error");
        return;
      }

      // Process the submission with reference check
      handleSubmitWithReferenceCheck(currentPromptInput);
    },
    [
      buttonDisabled,
      promptInput,
      t,
      handleSubmitWithReferenceCheck,
      textareaRef,
    ]
  );

  useEffect(() => {
    const handleSubmitPrompt = () => {
      // Don't proceed if button is disabled
      if (buttonDisabled) return;
      // Force re-read the current prompt input value to ensure we have the latest
      const currentPromptInput = textareaRef.current?.value || promptInput;

      // Check if there's any input to submit
      if (!currentPromptInput.trim()) {
        // Only show toast if there's an actual input attempt but it's empty
        // Skip showing toast for completely empty inputs
        if (currentPromptInput !== "") {
          showToast(t("prompt.error.empty"), "error");
        }
        return;
      }

      // Process the submission - use the current value from the textarea if available
      handleSubmitWithReferenceCheck(currentPromptInput);
    };

    // Add event listener for the submit prompt event
    window.addEventListener(SUBMIT_PROMPT_EVENT, handleSubmitPrompt);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener(SUBMIT_PROMPT_EVENT, handleSubmitPrompt);
    };
  }, [
    buttonDisabled,
    promptInput,
    t,
    handleSubmitWithReferenceCheck,
    textareaRef,
  ]);

  return (
    <>
      {buttonDisabled ? (
        <StopGenerationButton threadSlug={threadSlug} />
      ) : (
        <button
          className={`bg-primary rounded-full text-background p-2 ${
            disabled ? "opacity-70 hover:cursor-not-allowed" : "opacity-100"
          }`}
          ref={formRef}
          disabled={disabled}
          type="submit"
          data-tooltip-id="send-prompt"
          aria-label={t("common.ask-legal")}
          data-tooltip-content={t("workspace-chats.prompt.placeholder")}
          onClick={handleSubmit}
        >
          <LuArrowRight className="size-6" />
        </button>
      )}

      <ReferenceWarningModal
        isOpen={showReferenceWarning}
        onClose={() => {
          setShowReferenceWarning(false);
          setPendingPrompt("");
        }}
        onProceed={handleProceedWithoutReference}
      />

      <RexorLoginModal
        show={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onSuccess={async () => {
          setShowLoginModal(false);
          handleUpdateTransaction();
        }}
      />
    </>
  );
}
