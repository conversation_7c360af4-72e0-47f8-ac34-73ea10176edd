import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import ProgressModal from "@/components/Modals/ProgressModal";
import AbortWarningModal from "@/components/Modals/AbortWarningModal";
import useThreadProgress from "@/hooks/useThreadProgress";
import Progress from "@/components/ui/Progress";
import ChatError from "./ChatError";

/**
 * Calculate incremental progress based on main steps and sub-tasks
 * @param {number} currentStep - Current main step (1-based)
 * @param {number} totalSteps - Total number of main steps
 * @param {Array} stepDetails - Array of step details with sub-tasks
 * @param {boolean} isCompleted - Whether the entire process is completed
 * @returns {number} Progress percentage (0-100)
 */
const calculateIncrementalProgress = (
  currentStep,
  totalSteps,
  stepDetails,
  isCompleted
) => {
  if (isCompleted) return 100;
  if (totalSteps === 0) return 100;
  if (currentStep < 1) return 0;

  let totalProgress = 0;
  const progressPerStep = 100 / totalSteps;

  // Calculate progress for each step
  for (let step = 1; step <= totalSteps; step++) {
    const stepDetail = stepDetails.find((detail) => detail.step === step);

    if (step < currentStep) {
      // Previous steps are fully complete
      totalProgress += progressPerStep;
    } else if (step === currentStep) {
      // Current step - calculate based on sub-tasks
      if (stepDetail && stepDetail.subTasks && stepDetail.subTasks.length > 0) {
        const actualSubTasks = stepDetail.subTasks.filter(
          (st) => !st.isPlaceholder
        );
        const completedSubTasks = actualSubTasks.filter(
          (st) => st.status === "complete"
        ).length;

        // Calculate expected total sub-tasks
        let expectedTotal = stepDetail.expectedTotal || 0;

        // If we don't have an expected total, use the current actual sub-tasks count
        if (expectedTotal === 0) {
          expectedTotal = Math.max(actualSubTasks.length, 1);
        }

        // Calculate sub-task completion percentage
        const subTaskProgress =
          expectedTotal > 0
            ? Math.min(completedSubTasks / expectedTotal, 1)
            : 0;

        // Add partial progress for current step
        totalProgress += progressPerStep * subTaskProgress;
      } else {
        // No sub-tasks, assume some progress for being on this step
        totalProgress += progressPerStep * 0.1; // 10% progress for starting the step
      }
    }
    // Future steps (step > currentStep) contribute 0 progress
  }

  return Math.round(Math.min(totalProgress, 100));
};

export default function ChatProgress({ threadSlug = "", updateHistory }) {
  const { t } = useTranslation();
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false);
  const [isAbortModalOpen, setIsAbortModalOpen] = useState(false);
  const [isErrorDismissed, setIsErrorDismissed] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const {
    isActive,
    currentStep,
    totalSteps,
    flowType,
    cancel,
    error,
    clearError,
    isCompleted,
    completionTime,
    stepDetails,
  } = useThreadProgress(threadSlug);

  useEffect(() => {
    if (threadSlug && (isActive || isCompleted)) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 500);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [threadSlug, isActive, isCompleted]);

  useEffect(() => {
    setIsErrorDismissed(false);
  }, [threadSlug]);

  useEffect(() => {
    if (isActive) {
      setIsErrorDismissed(false);
    }
  }, [isActive]);

  const addCancellationMessage = () => {
    if (updateHistory) {
      const cancellationMessage = {
        uuid: `cancelled-${Date.now()}`,
        type: "statusResponse",
        content: t("chatProgress.cancelled"),
        role: "assistant",
        closed: true,
        error: null,
        animate: false,
        pending: false,
      };

      updateHistory((prevHistory) => [...prevHistory, cancellationMessage]);
    }
  };

  const handleDismissError = () => {
    setIsErrorDismissed(true);
    addCancellationMessage();
    clearError();
  };

  if (error && !isErrorDismissed) {
    return <ChatError error={error} onDismiss={handleDismissError} />;
  }

  if (!threadSlug || (!isActive && !isCompleted)) return null;

  const handleAbort = () => {
    cancel();
    addCancellationMessage();
  };

  const handleShowAbortModal = () => {
    setIsAbortModalOpen(true);
  };

  const handleCancelAbort = () => {
    setIsAbortModalOpen(false);
  };

  const handleConfirmAbort = () => {
    setIsAbortModalOpen(false);
    handleAbort();
  };

  const handleViewDetails = () => {
    setIsProgressModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsProgressModalOpen(false);
  };

  // Use the new incremental progress calculation
  const progressValue = calculateIncrementalProgress(
    currentStep,
    totalSteps,
    stepDetails,
    isCompleted
  );

  const stepKey =
    flowType && currentStep
      ? `chatProgress.types.${flowType}.step${currentStep}`
      : null;

  const stepLabel = isCompleted
    ? t("chatProgress.completed")
    : stepKey
      ? t(`${stepKey}.label`)
      : `${t("chatProgress.step")} ${currentStep}`;
  const stepDescription = isCompleted
    ? t("chatProgress.completedDescription")
    : stepKey
      ? t(`${stepKey}.desc`)
      : t("chatProgress.processing");

  return (
    <>
      <div
        className={`flex flex-col gap-6 max-w-[26rem] px-5 pt-5 pb-6 rounded-xl border bg-elevated transition-all duration-300 transform ${
          isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-8"
        }`}
      >
        <div className="flex items-start justify-between gap-4">
          <div className="flex flex-col gap-1 max-w-[18rem]">
            <h4 className="text-lg font-medium truncate">{stepLabel}</h4>
            <p className="text-muted font-medium line-clamp-2">
              {stepDescription}
            </p>
          </div>

          <div className="flex flex-col items-center gap-3 w-20">
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              className="w-full"
            >
              {t("chatProgress.details")}
            </Button>
            {!isCompleted && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleShowAbortModal}
                className="w-full text-red-600"
              >
                {t("chatProgress.abort")}
              </Button>
            )}
          </div>
        </div>

        <Progress value={progressValue} />
      </div>

      <ProgressModal
        isOpen={isProgressModalOpen}
        onClose={handleCloseModal}
        threadSlug={threadSlug}
      />

      <AbortWarningModal
        isOpen={isAbortModalOpen}
        onCancel={handleCancelAbort}
        onAbort={handleConfirmAbort}
      />
    </>
  );
}
